{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport default function DashboardHome() {\n  const [userRole, setUserRole] = useState<'admin' | 'consultant' | 'project-manager' | 'trainee'>('admin');\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockUserRole = localStorage.getItem('userRole') as 'admin' | 'consultant' | 'project-manager' | 'trainee' || 'admin';\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    \n    setUserRole(mockUserRole);\n    setLanguage(mockLanguage);\n  }, []);\n\n  const content = {\n    en: {\n      welcome: 'Welcome to DTC Accelerator',\n      adminDashboard: 'Admin Dashboard',\n      consultantDashboard: 'Consultant Dashboard',\n      projectManagerDashboard: 'Project Manager Dashboard',\n      traineeDashboard: 'Trainee Dashboard',\n      overview: 'Dashboard Overview',\n      description: 'This is your personalized dashboard based on your role and permissions.',\n      quickStats: 'Quick Statistics',\n      recentActivity: 'Recent Activity'\n    },\n    ar: {\n      welcome: 'مرحباً بك في مسرع التحول الرقمي',\n      adminDashboard: 'لوحة تحكم المدير',\n      consultantDashboard: 'لوحة تحكم المستشار',\n      projectManagerDashboard: 'لوحة تحكم مدير المشروع',\n      traineeDashboard: 'لوحة تحكم المتدرب',\n      overview: 'نظرة عامة على لوحة التحكم',\n      description: 'هذه لوحة التحكم الشخصية الخاصة بك بناءً على دورك وصلاحياتك.',\n      quickStats: 'إحصائيات سريعة',\n      recentActivity: 'النشاط الأخير'\n    }\n  };\n\n  const getDashboardTitle = () => {\n    switch (userRole) {\n      case 'admin':\n        return content[language].adminDashboard;\n      case 'consultant':\n        return content[language].consultantDashboard;\n      case 'project-manager':\n        return content[language].projectManagerDashboard;\n      case 'trainee':\n        return content[language].traineeDashboard;\n      default:\n        return content[language].adminDashboard;\n    }\n  };\n\n  const getStatsForRole = () => {\n    switch (userRole) {\n      case 'admin':\n        return [\n          { label: language === 'en' ? 'Total Users' : 'إجمالي المستخدمين', value: '1,234', icon: '👥' },\n          { label: language === 'en' ? 'Active Projects' : 'المشاريع النشطة', value: '56', icon: '📊' },\n          { label: language === 'en' ? 'Frameworks' : 'الأطر', value: '12', icon: '🏗️' },\n          { label: language === 'en' ? 'Training Courses' : 'الدورات التدريبية', value: '89', icon: '🎓' }\n        ];\n      case 'consultant':\n        return [\n          { label: language === 'en' ? 'My Projects' : 'مشاريعي', value: '8', icon: '📊' },\n          { label: language === 'en' ? 'Consultations' : 'الاستشارات', value: '24', icon: '💼' },\n          { label: language === 'en' ? 'Completed Tasks' : 'المهام المكتملة', value: '156', icon: '✅' }\n        ];\n      case 'project-manager':\n        return [\n          { label: language === 'en' ? 'Managed Projects' : 'المشاريع المدارة', value: '12', icon: '📊' },\n          { label: language === 'en' ? 'Team Members' : 'أعضاء الفريق', value: '45', icon: '👥' },\n          { label: language === 'en' ? 'Milestones' : 'المعالم', value: '78', icon: '🎯' }\n        ];\n      case 'trainee':\n        return [\n          { label: language === 'en' ? 'Enrolled Courses' : 'الدورات المسجلة', value: '6', icon: '🎓' },\n          { label: language === 'en' ? 'Completed Modules' : 'الوحدات المكتملة', value: '23', icon: '✅' },\n          { label: language === 'en' ? 'Certificates' : 'الشهادات', value: '3', icon: '🏆' }\n        ];\n      default:\n        return [];\n    }\n  };\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className={`text-3xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].welcome}\n        </h1>\n        <h2 className={`text-xl ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--emerald-green)' }}>\n          {getDashboardTitle()}\n        </h2>\n      </div>\n\n      {/* Overview Card */}\n      <div className=\"bg-white rounded-xl shadow-lg p-8 mb-8 card-shadow\">\n        <h3 className={`text-xl font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].overview}\n        </h3>\n        <p className={`text-gray-600 leading-relaxed ${language === 'ar' ? 'font-arabic' : ''}`}>\n          {content[language].description}\n        </p>\n      </div>\n\n      {/* Quick Statistics */}\n      <div className=\"mb-8\">\n        <h3 className={`text-lg font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].quickStats}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {getStatsForRole().map((stat, index) => (\n            <div key={index} className=\"bg-white rounded-xl shadow-lg p-6 card-shadow hover:shadow-xl transition-all duration-300\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div\n                  className=\"w-14 h-14 rounded-xl flex items-center justify-center text-white shadow-lg\"\n                  style={{ backgroundColor: 'var(--emerald-green)' }}\n                >\n                  <svg className=\"w-7 h-7\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                  <p className={`text-3xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {stat.value}\n                  </p>\n                  <p className={`text-sm text-gray-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {stat.label}\n                  </p>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"bg-white rounded-xl shadow-lg p-8 card-shadow\">\n        <h3 className={`text-xl font-semibold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].recentActivity}\n        </h3>\n        <div className=\"space-y-4\">\n          {[1, 2, 3].map((item) => (\n            <div key={item} className={`flex items-center p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <div\n                className=\"w-10 h-10 rounded-xl flex items-center justify-center text-white shadow-sm\"\n                style={{ backgroundColor: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                <p className={`text-sm font-semibold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {language === 'en' ? `Recent activity item ${item}` : `عنصر النشاط الأخير ${item}`}\n                </p>\n                <p className={`text-xs text-gray-500 mt-1 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                  {language === 'en' ? '2 hours ago' : 'منذ ساعتين'}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0D;IACjG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAAyE;YACnH,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YAExE,YAAY;YACZ,YAAY;QACd;kCAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,SAAS;YACT,gBAAgB;YAChB,qBAAqB;YACrB,yBAAyB;YACzB,kBAAkB;YAClB,UAAU;YACV,aAAa;YACb,YAAY;YACZ,gBAAgB;QAClB;QACA,IAAI;YACF,SAAS;YACT,gBAAgB;YAChB,qBAAqB;YACrB,yBAAyB;YACzB,kBAAkB;YAClB,UAAU;YACV,aAAa;YACb,YAAY;YACZ,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,cAAc;YACzC,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,mBAAmB;YAC9C,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,uBAAuB;YAClD,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,gBAAgB;YAC3C;gBACE,OAAO,OAAO,CAAC,SAAS,CAAC,cAAc;QAC3C;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,gBAAgB;wBAAqB,OAAO;wBAAS,MAAM;oBAAK;oBAC7F;wBAAE,OAAO,aAAa,OAAO,oBAAoB;wBAAmB,OAAO;wBAAM,MAAM;oBAAK;oBAC5F;wBAAE,OAAO,aAAa,OAAO,eAAe;wBAAS,OAAO;wBAAM,MAAM;oBAAM;oBAC9E;wBAAE,OAAO,aAAa,OAAO,qBAAqB;wBAAqB,OAAO;wBAAM,MAAM;oBAAK;iBAChG;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,gBAAgB;wBAAW,OAAO;wBAAK,MAAM;oBAAK;oBAC/E;wBAAE,OAAO,aAAa,OAAO,kBAAkB;wBAAc,OAAO;wBAAM,MAAM;oBAAK;oBACrF;wBAAE,OAAO,aAAa,OAAO,oBAAoB;wBAAmB,OAAO;wBAAO,MAAM;oBAAI;iBAC7F;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,qBAAqB;wBAAoB,OAAO;wBAAM,MAAM;oBAAK;oBAC9F;wBAAE,OAAO,aAAa,OAAO,iBAAiB;wBAAgB,OAAO;wBAAM,MAAM;oBAAK;oBACtF;wBAAE,OAAO,aAAa,OAAO,eAAe;wBAAW,OAAO;wBAAM,MAAM;oBAAK;iBAChF;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,qBAAqB;wBAAmB,OAAO;wBAAK,MAAM;oBAAK;oBAC5F;wBAAE,OAAO,aAAa,OAAO,sBAAsB;wBAAoB,OAAO;wBAAM,MAAM;oBAAI;oBAC9F;wBAAE,OAAO,aAAa,OAAO,iBAAiB;wBAAY,OAAO;wBAAK,MAAM;oBAAK;iBAClF;YACH;gBACE,OAAO,EAAE;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,GAAiD,OAA/C,aAAa,OAAO,eAAe;;0BAEpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;wBAAM,OAAO;4BAAE,OAAO;wBAAuB;kCACxH,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;kCAE5B,6LAAC;wBAAG,WAAW,AAAC,WAAiD,OAAvC,aAAa,OAAO,gBAAgB;wBAAM,OAAO;4BAAE,OAAO;wBAAuB;kCACxG;;;;;;;;;;;;0BAKL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;wBAAM,OAAO;4BAAE,OAAO;wBAAuB;kCAC3H,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;kCAE7B,6LAAC;wBAAE,WAAW,AAAC,iCAAuE,OAAvC,aAAa,OAAO,gBAAgB;kCAChF,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;wBAAM,OAAO;4BAAE,OAAO;wBAAuB;kCAC3H,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,MAAM,sBAC5B,6LAAC;gCAAgB,WAAU;0CACzB,cAAA,6LAAC;oCAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;sDAC5E,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB;4CAAuB;sDAEjD,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAI,WAAW,AAAC,GAA2D,OAAzD,aAAa,OAAO,oBAAoB;;8DACzD,6LAAC;oDAAE,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;oDAAM,OAAO;wDAAE,OAAO;oDAAuB;8DAClH,KAAK,KAAK;;;;;;8DAEb,6LAAC;oDAAE,WAAW,AAAC,qCAA2E,OAAvC,aAAa,OAAO,gBAAgB;8DACpF,KAAK,KAAK;;;;;;;;;;;;;;;;;;+BAfT;;;;;;;;;;;;;;;;0BAyBhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;wBAAM,OAAO;4BAAE,OAAO;wBAAuB;kCAC3H,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;kCAEnC,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,qBACd,6LAAC;gCAAe,WAAW,AAAC,mFAAsI,OAApD,aAAa,OAAO,qBAAqB;;kDACrJ,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAuB;kDAEjD,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAI,WAAW,AAAC,GAA2D,OAAzD,aAAa,OAAO,oBAAoB;;0DACzD,6LAAC;gDAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;gDAAM,OAAO;oDAAE,OAAO;gDAAuB;0DACrH,aAAa,OAAO,AAAC,wBAA4B,OAAL,QAAS,AAAC,sBAA0B,OAAL;;;;;;0DAE9E,6LAAC;gDAAE,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;0DAC7E,aAAa,OAAO,gBAAgB;;;;;;;;;;;;;+BAdjC;;;;;;;;;;;;;;;;;;;;;;AAuBtB;GAvKwB;KAAA", "debugId": null}}]}