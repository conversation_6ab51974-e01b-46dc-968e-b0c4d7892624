{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div \n      className={`relative overflow-hidden mb-8 ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-0 left-0 w-full h-full\">\n          <svg className=\"w-full h-full\" viewBox=\"0 0 100 100\" preserveAspectRatio=\"none\">\n            <defs>\n              <pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\">\n                <path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"white\" strokeWidth=\"0.5\"/>\n              </pattern>\n            </defs>\n            <rect width=\"100\" height=\"100\" fill=\"url(#grid)\" />\n          </svg>\n        </div>\n      </div>\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-8 py-12\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAYe,SAAS,KAAK,KAA8D;QAA9D,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa,GAA9D;;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;QACd;yBAAG,EAAE;IAEL,qBACE,6LAAC;QACC,WAAW,AAAC,iCAA+E,OAA/C,aAAa,OAAO,eAAe;QAC/E,OAAO;YACL,YAAY;QACd;;0BAGA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAgB,SAAQ;wBAAc,qBAAoB;;0CACvE,6LAAC;0CACC,cAAA,6LAAC;oCAAQ,IAAG;oCAAO,OAAM;oCAAK,QAAO;oCAAK,cAAa;8CACrD,cAAA,6LAAC;wCAAK,GAAE;wCAAoB,MAAK;wCAAO,QAAO;wCAAQ,aAAY;;;;;;;;;;;;;;;;0CAGvE,6LAAC;gCAAK,OAAM;gCAAM,QAAO;gCAAM,MAAK;;;;;;;;;;;;;;;;;;;;;;0BAM1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,oDAA4F,OAAzC,aAAa,OAAO,YAAY;;;;;;kCACpG,6LAAC;wBAAI,WAAW,AAAC,gEAA0G,OAA3C,aAAa,OAAO,YAAY;;;;;;kCAChH,6LAAC;wBAAI,WAAW,AAAC,sDAAkG,OAA7C,aAAa,OAAO,aAAa;;;;;;;;;;;;0BAIzG,6LAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAW,AAAC,qDAAgH,OAA5D,aAAa,OAAO,qCAAqC;sCAC1H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,6LAAC;4CACC,WAAW,AAAC,WAAyD,OAA/C,aAAa,OAAO,oBAAoB;4CAC9D,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,6LAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,6LAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,6LAAC;wBAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;4BAE3E,sBACC,6LAAC;gCAAI,WAAW,AAAC,iBAAoD,OAApC,aAAa,OAAO,SAAS;0CAC5D,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;;oCAEZ,0BACC,6LAAC;wCAAE,WAAW,AAAC,mEAAyG,OAAvC,aAAa,OAAO,gBAAgB;kDAClH;;;;;;kDAKL,6LAAC;wCAAG,WAAW,AAAC,gEAAsG,OAAvC,aAAa,OAAO,gBAAgB;kDAChH;;;;;;oCAIF,6BACC,6LAAC;wCAAE,WAAW,AAAC,mDAAyF,OAAvC,aAAa,OAAO,gBAAgB;kDAClG;;;;;;;;;;;;;;;;;;kCAOT,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GAlHwB;KAAA", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Hero from '../../components/Hero';\n\nexport default function DashboardHome() {\n  const [userRole, setUserRole] = useState<'admin' | 'consultant' | 'project-manager' | 'trainee'>('admin');\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockUserRole = localStorage.getItem('userRole') as 'admin' | 'consultant' | 'project-manager' | 'trainee' || 'admin';\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    \n    setUserRole(mockUserRole);\n    setLanguage(mockLanguage);\n  }, []);\n\n  const content = {\n    en: {\n      welcome: 'Welcome to DTC Accelerator',\n      adminDashboard: 'Admin Dashboard',\n      consultantDashboard: 'Consultant Dashboard',\n      projectManagerDashboard: 'Project Manager Dashboard',\n      traineeDashboard: 'Trainee Dashboard',\n      overview: 'Dashboard Overview',\n      description: 'This is your personalized dashboard based on your role and permissions.',\n      quickStats: 'Quick Statistics',\n      recentActivity: 'Recent Activity'\n    },\n    ar: {\n      welcome: 'مرحباً بك في مسرع التحول الرقمي',\n      adminDashboard: 'لوحة تحكم المدير',\n      consultantDashboard: 'لوحة تحكم المستشار',\n      projectManagerDashboard: 'لوحة تحكم مدير المشروع',\n      traineeDashboard: 'لوحة تحكم المتدرب',\n      overview: 'نظرة عامة على لوحة التحكم',\n      description: 'هذه لوحة التحكم الشخصية الخاصة بك بناءً على دورك وصلاحياتك.',\n      quickStats: 'إحصائيات سريعة',\n      recentActivity: 'النشاط الأخير'\n    }\n  };\n\n  const getDashboardTitle = () => {\n    switch (userRole) {\n      case 'admin':\n        return content[language].adminDashboard;\n      case 'consultant':\n        return content[language].consultantDashboard;\n      case 'project-manager':\n        return content[language].projectManagerDashboard;\n      case 'trainee':\n        return content[language].traineeDashboard;\n      default:\n        return content[language].adminDashboard;\n    }\n  };\n\n  const getStatsForRole = () => {\n    switch (userRole) {\n      case 'admin':\n        return [\n          { label: language === 'en' ? 'Total Users' : 'إجمالي المستخدمين', value: '1,234', icon: '👥' },\n          { label: language === 'en' ? 'Active Projects' : 'المشاريع النشطة', value: '56', icon: '📊' },\n          { label: language === 'en' ? 'Frameworks' : 'الأطر', value: '12', icon: '🏗️' },\n          { label: language === 'en' ? 'Training Courses' : 'الدورات التدريبية', value: '89', icon: '🎓' }\n        ];\n      case 'consultant':\n        return [\n          { label: language === 'en' ? 'My Projects' : 'مشاريعي', value: '8', icon: '📊' },\n          { label: language === 'en' ? 'Consultations' : 'الاستشارات', value: '24', icon: '💼' },\n          { label: language === 'en' ? 'Completed Tasks' : 'المهام المكتملة', value: '156', icon: '✅' }\n        ];\n      case 'project-manager':\n        return [\n          { label: language === 'en' ? 'Managed Projects' : 'المشاريع المدارة', value: '12', icon: '📊' },\n          { label: language === 'en' ? 'Team Members' : 'أعضاء الفريق', value: '45', icon: '👥' },\n          { label: language === 'en' ? 'Milestones' : 'المعالم', value: '78', icon: '🎯' }\n        ];\n      case 'trainee':\n        return [\n          { label: language === 'en' ? 'Enrolled Courses' : 'الدورات المسجلة', value: '6', icon: '🎓' },\n          { label: language === 'en' ? 'Completed Modules' : 'الوحدات المكتملة', value: '23', icon: '✅' },\n          { label: language === 'en' ? 'Certificates' : 'الشهادات', value: '3', icon: '🏆' }\n        ];\n      default:\n        return [];\n    }\n  };\n\n  const getHeroIcon = () => {\n    switch (userRole) {\n      case 'admin':\n        return (\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n          </svg>\n        );\n      case 'consultant':\n        return (\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6\" />\n          </svg>\n        );\n      case 'project-manager':\n        return (\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n          </svg>\n        );\n      case 'trainee':\n        return (\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n          </svg>\n        );\n      default:\n        return (\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n          </svg>\n        );\n    }\n  };\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      {/* Hero Section */}\n      <Hero\n        title={content[language].welcome}\n        subtitle={getDashboardTitle()}\n        description={content[language].description}\n        icon={getHeroIcon()}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم' },\n          { label: language === 'en' ? 'Home' : 'الرئيسية' }\n        ]}\n      />\n\n      {/* Overview Card */}\n      <div className=\"bg-white rounded-xl shadow-lg p-8 mb-8 card-shadow\">\n        <h3 className={`text-xl font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].overview}\n        </h3>\n        <p className={`text-gray-600 leading-relaxed ${language === 'ar' ? 'font-arabic' : ''}`}>\n          {content[language].description}\n        </p>\n      </div>\n\n      {/* Quick Statistics */}\n      <div className=\"mb-8\">\n        <h3 className={`text-lg font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].quickStats}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {getStatsForRole().map((stat, index) => (\n            <div key={index} className=\"bg-white rounded-xl shadow-lg p-6 card-shadow hover:shadow-xl transition-all duration-300\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div\n                  className=\"w-14 h-14 rounded-xl flex items-center justify-center text-white shadow-lg\"\n                  style={{ backgroundColor: 'var(--emerald-green)' }}\n                >\n                  <svg className=\"w-7 h-7\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                  </svg>\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                  <p className={`text-3xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {stat.value}\n                  </p>\n                  <p className={`text-sm text-gray-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {stat.label}\n                  </p>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"bg-white rounded-xl shadow-lg p-8 card-shadow\">\n        <h3 className={`text-xl font-semibold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].recentActivity}\n        </h3>\n        <div className=\"space-y-4\">\n          {[1, 2, 3].map((item) => (\n            <div key={item} className={`flex items-center p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <div\n                className=\"w-10 h-10 rounded-xl flex items-center justify-center text-white shadow-sm\"\n                style={{ backgroundColor: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                <p className={`text-sm font-semibold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {language === 'en' ? `Recent activity item ${item}` : `عنصر النشاط الأخير ${item}`}\n                </p>\n                <p className={`text-xs text-gray-500 mt-1 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                  {language === 'en' ? '2 hours ago' : 'منذ ساعتين'}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0D;IACjG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAAyE;YACnH,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YAExE,YAAY;YACZ,YAAY;QACd;kCAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,SAAS;YACT,gBAAgB;YAChB,qBAAqB;YACrB,yBAAyB;YACzB,kBAAkB;YAClB,UAAU;YACV,aAAa;YACb,YAAY;YACZ,gBAAgB;QAClB;QACA,IAAI;YACF,SAAS;YACT,gBAAgB;YAChB,qBAAqB;YACrB,yBAAyB;YACzB,kBAAkB;YAClB,UAAU;YACV,aAAa;YACb,YAAY;YACZ,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,cAAc;YACzC,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,mBAAmB;YAC9C,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,uBAAuB;YAClD,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,gBAAgB;YAC3C;gBACE,OAAO,OAAO,CAAC,SAAS,CAAC,cAAc;QAC3C;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,gBAAgB;wBAAqB,OAAO;wBAAS,MAAM;oBAAK;oBAC7F;wBAAE,OAAO,aAAa,OAAO,oBAAoB;wBAAmB,OAAO;wBAAM,MAAM;oBAAK;oBAC5F;wBAAE,OAAO,aAAa,OAAO,eAAe;wBAAS,OAAO;wBAAM,MAAM;oBAAM;oBAC9E;wBAAE,OAAO,aAAa,OAAO,qBAAqB;wBAAqB,OAAO;wBAAM,MAAM;oBAAK;iBAChG;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,gBAAgB;wBAAW,OAAO;wBAAK,MAAM;oBAAK;oBAC/E;wBAAE,OAAO,aAAa,OAAO,kBAAkB;wBAAc,OAAO;wBAAM,MAAM;oBAAK;oBACrF;wBAAE,OAAO,aAAa,OAAO,oBAAoB;wBAAmB,OAAO;wBAAO,MAAM;oBAAI;iBAC7F;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,qBAAqB;wBAAoB,OAAO;wBAAM,MAAM;oBAAK;oBAC9F;wBAAE,OAAO,aAAa,OAAO,iBAAiB;wBAAgB,OAAO;wBAAM,MAAM;oBAAK;oBACtF;wBAAE,OAAO,aAAa,OAAO,eAAe;wBAAW,OAAO;wBAAM,MAAM;oBAAK;iBAChF;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,qBAAqB;wBAAmB,OAAO;wBAAK,MAAM;oBAAK;oBAC5F;wBAAE,OAAO,aAAa,OAAO,sBAAsB;wBAAoB,OAAO;wBAAM,MAAM;oBAAI;oBAC9F;wBAAE,OAAO,aAAa,OAAO,iBAAiB;wBAAY,OAAO;wBAAK,MAAM;oBAAK;iBAClF;YACH;gBACE,OAAO,EAAE;QACb;IACF;IAEA,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;gBACE,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;QAG7E;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,GAAiD,OAA/C,aAAa,OAAO,eAAe;;0BAEpD,6LAAC,6HAAA,CAAA,UAAI;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,OAAO;gBAChC,UAAU;gBACV,aAAa,OAAO,CAAC,SAAS,CAAC,WAAW;gBAC1C,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;oBAAc;oBACzD;wBAAE,OAAO,aAAa,OAAO,SAAS;oBAAW;iBAClD;;;;;;0BAIH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;wBAAM,OAAO;4BAAE,OAAO;wBAAuB;kCAC3H,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;kCAE7B,6LAAC;wBAAE,WAAW,AAAC,iCAAuE,OAAvC,aAAa,OAAO,gBAAgB;kCAChF,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;wBAAM,OAAO;4BAAE,OAAO;wBAAuB;kCAC3H,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,MAAM,sBAC5B,6LAAC;gCAAgB,WAAU;0CACzB,cAAA,6LAAC;oCAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;sDAC5E,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB;4CAAuB;sDAEjD,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAI,WAAW,AAAC,GAA2D,OAAzD,aAAa,OAAO,oBAAoB;;8DACzD,6LAAC;oDAAE,WAAW,AAAC,sBAA4D,OAAvC,aAAa,OAAO,gBAAgB;oDAAM,OAAO;wDAAE,OAAO;oDAAuB;8DAClH,KAAK,KAAK;;;;;;8DAEb,6LAAC;oDAAE,WAAW,AAAC,qCAA2E,OAAvC,aAAa,OAAO,gBAAgB;8DACpF,KAAK,KAAK;;;;;;;;;;;;;;;;;;+BAfT;;;;;;;;;;;;;;;;0BAyBhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;wBAAM,OAAO;4BAAE,OAAO;wBAAuB;kCAC3H,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;kCAEnC,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,qBACd,6LAAC;gCAAe,WAAW,AAAC,mFAAsI,OAApD,aAAa,OAAO,qBAAqB;;kDACrJ,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAuB;kDAEjD,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAI,WAAW,AAAC,GAA2D,OAAzD,aAAa,OAAO,oBAAoB;;0DACzD,6LAAC;gDAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;gDAAM,OAAO;oDAAE,OAAO;gDAAuB;0DACrH,aAAa,OAAO,AAAC,wBAA4B,OAAL,QAAS,AAAC,sBAA0B,OAAL;;;;;;0DAE9E,6LAAC;gDAAE,WAAW,AAAC,8BAAoE,OAAvC,aAAa,OAAO,gBAAgB;0DAC7E,aAAa,OAAO,gBAAgB;;;;;;;;;;;;;+BAdjC;;;;;;;;;;;;;;;;;;;;;;AAuBtB;GA5MwB;KAAA", "debugId": null}}]}