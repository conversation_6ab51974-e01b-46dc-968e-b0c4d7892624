{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function LoginPage() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    role: ''\n  });\n\n  const roles = [\n    { value: 'admin', labelEn: 'Admin', labelAr: 'مدير' },\n    { value: 'consultant', labelEn: 'Consultant', labelAr: 'مستشار' },\n    { value: 'project-manager', labelEn: 'Project Manager', labelAr: 'مدير مشروع' },\n    { value: 'trainee', labelEn: 'Trainee', labelAr: 'متدرب' }\n  ];\n\n  const text = {\n    en: {\n      title: 'Welcome Back',\n      subtitle: 'Sign in to your account',\n      email: 'Email Address',\n      password: 'Password',\n      role: 'Select Role',\n      signIn: 'Sign In',\n      forgotPassword: 'Forgot Password?',\n      companyName: 'DTC Accelerator',\n      tagline: 'Empowering Digital Transformation'\n    },\n    ar: {\n      title: 'مرحباً بعودتك',\n      subtitle: 'تسجيل الدخول إلى حسابك',\n      email: 'عنوان البريد الإلكتروني',\n      password: 'كلمة المرور',\n      role: 'اختر الدور',\n      signIn: 'تسجيل الدخول',\n      forgotPassword: 'نسيت كلمة المرور؟',\n      companyName: 'مسرع التحول الرقمي',\n      tagline: 'تمكين التحول الرقمي'\n    }\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Mock authentication - just log the data\n    console.log('Login attempt:', formData);\n    alert(`Mock login successful for ${formData.email} as ${formData.role}`);\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen flex\" dir={language === 'ar' ? 'rtl' : 'ltr'}>\n      {/* Left Side - Animated Image Section (70%) */}\n      <div className=\"hidden lg:flex lg:w-[70%] bg-gradient-to-br from-emerald-600 via-emerald-700 to-emerald-800 relative overflow-hidden\" style={{\n        background: `linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)`\n      }}>\n        {/* Background Pattern */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"absolute top-20 left-20 w-32 h-32 bg-white rounded-full float-animation\"></div>\n          <div className=\"absolute top-40 right-32 w-24 h-24 bg-white rounded-full float-animation\" style={{animationDelay: '1s'}}></div>\n          <div className=\"absolute bottom-32 left-32 w-20 h-20 bg-white rounded-full float-animation\" style={{animationDelay: '2s'}}></div>\n          <div className=\"absolute bottom-20 right-20 w-28 h-28 bg-white rounded-full float-animation\" style={{animationDelay: '0.5s'}}></div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"flex flex-col justify-center items-center w-full p-12 text-white relative z-10\">\n          <div className=\"text-center fade-in-up\">\n            {/* Company Logo/Icon */}\n            <div className=\"mb-8 pulse-animation\">\n              <div className=\"w-24 h-24 bg-white rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-12 h-12\" style={{color: 'var(--emerald-green)'}} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\n                </svg>\n              </div>\n            </div>\n\n            <h1 className=\"text-5xl font-bold mb-4\">\n              {text[language].companyName}\n            </h1>\n            <p className=\"text-xl mb-8 opacity-90\">\n              {text[language].tagline}\n            </p>\n\n            {/* Business Graphics */}\n            <div className=\"grid grid-cols-3 gap-8 mt-12 opacity-80\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2 float-animation\">\n                  <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n                  </svg>\n                </div>\n                <p className=\"text-sm\">{language === 'ar' ? 'التحليلات' : 'Analytics'}</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2 float-animation\" style={{animationDelay: '0.5s'}}>\n                  <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                  </svg>\n                </div>\n                <p className=\"text-sm\">{language === 'ar' ? 'الجودة' : 'Quality'}</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2 float-animation\" style={{animationDelay: '1s'}}>\n                  <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-1 16H9V7h9v14z\"/>\n                  </svg>\n                </div>\n                <p className=\"text-sm\">{language === 'ar' ? 'الابتكار' : 'Innovation'}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Right Side - Login Form (30%) */}\n      <div className=\"w-full lg:w-[30%] flex items-center justify-center p-8 bg-gray-50\">\n        <div className=\"w-full max-w-sm slide-in-right\">\n          {/* Language Toggle */}\n          <div className={`flex mb-8 ${language === 'ar' ? 'justify-start' : 'justify-end'}`}>\n            <button\n              onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}\n              className=\"px-3 py-1.5 text-sm text-white rounded-md hover:opacity-90 transition-all duration-200 font-medium\"\n              style={{backgroundColor: '#026c4a'}}\n            >\n              {language === 'en' ? 'العربية' : 'English'}\n            </button>\n          </div>\n\n          <div className={language === 'ar' ? 'arabic' : 'english'} dir={language === 'ar' ? 'rtl' : 'ltr'}>\n            <div className={`mb-8 ${language === 'ar' ? 'text-right' : 'text-center'}`}>\n              <h2 className=\"text-3xl font-bold mb-3 text-gray-900\">\n                {text[language].title}\n              </h2>\n              <p className=\"text-gray-600 text-base\">\n                {text[language].subtitle}\n              </p>\n            </div>\n\n            <form onSubmit={handleSubmit} className=\"space-y-5\">\n              <div className=\"space-y-1\">\n                <label className={`block text-sm font-semibold ${language === 'ar' ? 'text-right' : 'text-left'}`} style={{color: '#374151'}}>\n                  {text[language].email}\n                </label>\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  required\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 ${language === 'ar' ? 'text-right' : 'text-left'}`}\n                  style={{\n                    fontSize: '16px',\n                    backgroundColor: '#ffffff',\n                    color: '#111827'\n                  }}\n                  placeholder={text[language].email}\n                />\n              </div>\n\n              <div className=\"space-y-1\">\n                <label className={`block text-sm font-semibold ${language === 'ar' ? 'text-right' : 'text-left'}`} style={{color: '#374151'}}>\n                  {text[language].password}\n                </label>\n                <input\n                  type=\"password\"\n                  name=\"password\"\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  required\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 ${language === 'ar' ? 'text-right' : 'text-left'}`}\n                  style={{\n                    fontSize: '16px',\n                    backgroundColor: '#ffffff',\n                    color: '#111827'\n                  }}\n                  placeholder={text[language].password}\n                />\n              </div>\n\n              <div className=\"space-y-1\">\n                <label className={`block text-sm font-semibold ${language === 'ar' ? 'text-right' : 'text-left'}`} style={{color: '#374151'}}>\n                  {text[language].role}\n                </label>\n                <select\n                  name=\"role\"\n                  value={formData.role}\n                  onChange={handleInputChange}\n                  required\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 ${language === 'ar' ? 'text-right' : 'text-left'}`}\n                  style={{\n                    fontSize: '16px',\n                    backgroundColor: '#ffffff',\n                    color: '#111827'\n                  }}\n                >\n                  <option value=\"\" style={{color: '#9CA3AF'}}>{text[language].role}</option>\n                  {roles.map((role) => (\n                    <option key={role.value} value={role.value} style={{color: '#111827'}}>\n                      {language === 'en' ? role.labelEn : role.labelAr}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div className=\"pt-2\">\n                <button\n                  type=\"submit\"\n                  className=\"w-full text-white py-3 px-4 rounded-md font-semibold text-base transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transform hover:scale-[1.02]\"\n                  style={{\n                    backgroundColor: '#026c4a',\n                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'\n                  }}\n                >\n                  {text[language].signIn}\n                </button>\n              </div>\n\n              <div className={`pt-2 ${language === 'ar' ? 'text-right' : 'text-center'}`}>\n                <a\n                  href=\"#\"\n                  className=\"text-sm font-medium transition-colors duration-200 hover:underline\"\n                  style={{color: '#026c4a'}}\n                >\n                  {text[language].forgotPassword}\n                </a>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAS,SAAS;YAAS,SAAS;QAAO;QACpD;YAAE,OAAO;YAAc,SAAS;YAAc,SAAS;QAAS;QAChE;YAAE,OAAO;YAAmB,SAAS;YAAmB,SAAS;QAAa;QAC9E;YAAE,OAAO;YAAW,SAAS;YAAW,SAAS;QAAQ;KAC1D;IAED,MAAM,OAAO;QACX,IAAI;YACF,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,gBAAgB;YAChB,aAAa;YACb,SAAS;QACX;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,gBAAgB;YAChB,aAAa;YACb,SAAS;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,0CAA0C;QAC1C,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,MAAM,AAAC,6BAAiD,OAArB,SAAS,KAAK,EAAC,QAAoB,OAAd,SAAS,IAAI;IACvE;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAoB,KAAK,aAAa,OAAO,QAAQ;;0BAElE,6LAAC;gBAAI,WAAU;gBAAuH,OAAO;oBAC3I,YAAa;gBACf;;kCAEE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;gCAA2E,OAAO;oCAAC,gBAAgB;gCAAI;;;;;;0CACtH,6LAAC;gCAAI,WAAU;gCAA6E,OAAO;oCAAC,gBAAgB;gCAAI;;;;;;0CACxH,6LAAC;gCAAI,WAAU;gCAA8E,OAAO;oCAAC,gBAAgB;gCAAM;;;;;;;;;;;;kCAI7H,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAY,OAAO;gDAAC,OAAO;4CAAsB;4CAAG,MAAK;4CAAe,SAAQ;sDAC7F,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;8CAKd,6LAAC;oCAAG,WAAU;8CACX,IAAI,CAAC,SAAS,CAAC,WAAW;;;;;;8CAE7B,6LAAC;oCAAE,WAAU;8CACV,IAAI,CAAC,SAAS,CAAC,OAAO;;;;;;8CAIzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAE,WAAU;8DAAW,aAAa,OAAO,cAAc;;;;;;;;;;;;sDAE5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAA4G,OAAO;wDAAC,gBAAgB;oDAAM;8DACvJ,cAAA,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAE,WAAU;8DAAW,aAAa,OAAO,WAAW;;;;;;;;;;;;sDAEzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAA4G,OAAO;wDAAC,gBAAgB;oDAAI;8DACrJ,cAAA,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAE,WAAU;8DAAW,aAAa,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAW,AAAC,aAAgE,OAApD,aAAa,OAAO,kBAAkB;sCACjE,cAAA,6LAAC;gCACC,SAAS,IAAM,YAAY,aAAa,OAAO,OAAO;gCACtD,WAAU;gCACV,OAAO;oCAAC,iBAAiB;gCAAS;0CAEjC,aAAa,OAAO,YAAY;;;;;;;;;;;sCAIrC,6LAAC;4BAAI,WAAW,aAAa,OAAO,WAAW;4BAAW,KAAK,aAAa,OAAO,QAAQ;;8CACzF,6LAAC;oCAAI,WAAW,AAAC,QAAwD,OAAjD,aAAa,OAAO,eAAe;;sDACzD,6LAAC;4CAAG,WAAU;sDACX,IAAI,CAAC,SAAS,CAAC,KAAK;;;;;;sDAEvB,6LAAC;4CAAE,WAAU;sDACV,IAAI,CAAC,SAAS,CAAC,QAAQ;;;;;;;;;;;;8CAI5B,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAW,AAAC,+BAA6E,OAA/C,aAAa,OAAO,eAAe;oDAAe,OAAO;wDAAC,OAAO;oDAAS;8DACxH,IAAI,CAAC,SAAS,CAAC,KAAK;;;;;;8DAEvB,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,QAAQ;oDACR,WAAW,AAAC,4KAA0N,OAA/C,aAAa,OAAO,eAAe;oDAC1N,OAAO;wDACL,UAAU;wDACV,iBAAiB;wDACjB,OAAO;oDACT;oDACA,aAAa,IAAI,CAAC,SAAS,CAAC,KAAK;;;;;;;;;;;;sDAIrC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAW,AAAC,+BAA6E,OAA/C,aAAa,OAAO,eAAe;oDAAe,OAAO;wDAAC,OAAO;oDAAS;8DACxH,IAAI,CAAC,SAAS,CAAC,QAAQ;;;;;;8DAE1B,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,QAAQ;oDACR,WAAW,AAAC,4KAA0N,OAA/C,aAAa,OAAO,eAAe;oDAC1N,OAAO;wDACL,UAAU;wDACV,iBAAiB;wDACjB,OAAO;oDACT;oDACA,aAAa,IAAI,CAAC,SAAS,CAAC,QAAQ;;;;;;;;;;;;sDAIxC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAW,AAAC,+BAA6E,OAA/C,aAAa,OAAO,eAAe;oDAAe,OAAO;wDAAC,OAAO;oDAAS;8DACxH,IAAI,CAAC,SAAS,CAAC,IAAI;;;;;;8DAEtB,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,QAAQ;oDACR,WAAW,AAAC,4KAA0N,OAA/C,aAAa,OAAO,eAAe;oDAC1N,OAAO;wDACL,UAAU;wDACV,iBAAiB;wDACjB,OAAO;oDACT;;sEAEA,6LAAC;4DAAO,OAAM;4DAAG,OAAO;gEAAC,OAAO;4DAAS;sEAAI,IAAI,CAAC,SAAS,CAAC,IAAI;;;;;;wDAC/D,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gEAAwB,OAAO,KAAK,KAAK;gEAAE,OAAO;oEAAC,OAAO;gEAAS;0EACjE,aAAa,OAAO,KAAK,OAAO,GAAG,KAAK,OAAO;+DADrC,KAAK,KAAK;;;;;;;;;;;;;;;;;sDAO7B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,OAAO;oDACL,iBAAiB;oDACjB,WAAW;gDACb;0DAEC,IAAI,CAAC,SAAS,CAAC,MAAM;;;;;;;;;;;sDAI1B,6LAAC;4CAAI,WAAW,AAAC,QAAwD,OAAjD,aAAa,OAAO,eAAe;sDACzD,cAAA,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAC,OAAO;gDAAS;0DAEvB,IAAI,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;GA5OwB;KAAA", "debugId": null}}, {"offset": {"line": 619, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}