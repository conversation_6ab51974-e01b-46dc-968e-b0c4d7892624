{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div\n      className={`relative overflow-hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-12 py-16\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAYe,SAAS,KAAK,KAA8D;QAA9D,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa,GAA9D;;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;QACd;yBAAG,EAAE;IAEL,qBACE,6LAAC;QACC,WAAW,AAAC,4BAA0E,OAA/C,aAAa,OAAO,eAAe;QAC1E,OAAO;YACL,YAAY;QACd;;0BAIA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,oDAA4F,OAAzC,aAAa,OAAO,YAAY;;;;;;kCACpG,6LAAC;wBAAI,WAAW,AAAC,gEAA0G,OAA3C,aAAa,OAAO,YAAY;;;;;;kCAChH,6LAAC;wBAAI,WAAW,AAAC,sDAAkG,OAA7C,aAAa,OAAO,aAAa;;;;;;;;;;;;0BAIzG,6LAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAW,AAAC,qDAAgH,OAA5D,aAAa,OAAO,qCAAqC;sCAC1H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,6LAAC;4CACC,WAAW,AAAC,WAAyD,OAA/C,aAAa,OAAO,oBAAoB;4CAC9D,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,6LAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,6LAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,6LAAC;wBAAI,WAAW,AAAC,qBAAwE,OAApD,aAAa,OAAO,qBAAqB;;4BAE3E,sBACC,6LAAC;gCAAI,WAAW,AAAC,iBAAoD,OAApC,aAAa,OAAO,SAAS;0CAC5D,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;;oCAEZ,0BACC,6LAAC;wCAAE,WAAW,AAAC,mEAAyG,OAAvC,aAAa,OAAO,gBAAgB;kDAClH;;;;;;kDAKL,6LAAC;wCAAG,WAAW,AAAC,gEAAsG,OAAvC,aAAa,OAAO,gBAAgB;kDAChH;;;;;;oCAIF,6BACC,6LAAC;wCAAE,WAAW,AAAC,mDAAyF,OAAvC,aAAa,OAAO,gBAAgB;kDAClG;;;;;;;;;;;;;;;;;;kCAOT,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GArGwB;KAAA", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/DAMAWheel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface DAMAWheelProps {\n  language: 'en' | 'ar';\n  onDomainClick: (domain: string) => void;\n}\n\nexport default function DAMAWheel({ language, onDomainClick }: DAMAWheelProps) {\n  const [selectedDomain, setSelectedDomain] = useState<string | null>(null);\n\n  const domains = [\n    {\n      id: 'data-governance',\n      name: { en: 'Data Governance', ar: 'حوكمة البيانات' },\n      angle: 0,\n      color: '#026c4a'\n    },\n    {\n      id: 'data-architecture',\n      name: { en: 'Data Architecture', ar: 'هندسة البيانات' },\n      angle: 45,\n      color: '#0c402e'\n    },\n    {\n      id: 'data-modeling',\n      name: { en: 'Data Modeling', ar: 'نمذجة البيانات' },\n      angle: 90,\n      color: '#026c4a'\n    },\n    {\n      id: 'data-storage',\n      name: { en: 'Data Storage', ar: 'تخزين البيانات' },\n      angle: 135,\n      color: '#0c402e'\n    },\n    {\n      id: 'data-security',\n      name: { en: 'Data Security', ar: 'أمان البيانات' },\n      angle: 180,\n      color: '#026c4a'\n    },\n    {\n      id: 'data-integration',\n      name: { en: 'Data Integration', ar: 'تكامل البيانات' },\n      angle: 225,\n      color: '#0c402e'\n    },\n    {\n      id: 'data-quality',\n      name: { en: 'Data Quality', ar: 'جودة البيانات' },\n      angle: 270,\n      color: '#026c4a'\n    },\n    {\n      id: 'metadata',\n      name: { en: 'Metadata', ar: 'البيانات الوصفية' },\n      angle: 315,\n      color: '#0c402e'\n    }\n  ];\n\n  const handleDomainClick = (domain: any) => {\n    setSelectedDomain(domain.id);\n    onDomainClick(domain.id);\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center p-8\">\n      <h3 className={`text-2xl font-bold mb-8 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n        {language === 'en' ? 'DAMA Data Management Domains' : 'مجالات إدارة البيانات DAMA'}\n      </h3>\n      \n      <div className=\"relative w-96 h-96\">\n        {/* Center Circle */}\n        <div \n          className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-2xl animate-pulse-subtle\"\n          style={{ backgroundColor: 'var(--emerald-green)' }}\n        >\n          <span className={language === 'ar' ? 'font-arabic' : ''}>\n            {language === 'en' ? 'DAMA' : 'داما'}\n          </span>\n        </div>\n\n        {/* Domain Segments */}\n        {domains.map((domain, index) => {\n          const radius = 150;\n          const centerX = 192;\n          const centerY = 192;\n          const angleRad = (domain.angle * Math.PI) / 180;\n          const x = centerX + radius * Math.cos(angleRad);\n          const y = centerY + radius * Math.sin(angleRad);\n\n          return (\n            <div\n              key={domain.id}\n              className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-500 hover:scale-110 ${\n                selectedDomain === domain.id ? 'scale-125 z-10' : ''\n              }`}\n              style={{\n                left: x,\n                top: y,\n                animationDelay: `${index * 0.1}s`\n              }}\n              onClick={() => handleDomainClick(domain)}\n            >\n              <div \n                className={`w-20 h-20 rounded-full flex items-center justify-center text-white font-semibold text-xs shadow-lg hover:shadow-2xl transition-all duration-300 ${\n                  selectedDomain === domain.id ? 'ring-4 ring-white' : ''\n                }`}\n                style={{ \n                  backgroundColor: domain.color,\n                  animation: 'fadeInUp 0.6s ease-out forwards'\n                }}\n              >\n                <span className={`text-center leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n                  {domain.name[language]}\n                </span>\n              </div>\n              \n              {/* Connection Line */}\n              <div \n                className=\"absolute top-1/2 left-1/2 origin-left h-0.5 bg-gray-300 opacity-50\"\n                style={{\n                  width: `${radius - 60}px`,\n                  transform: `translate(-50%, -50%) rotate(${domain.angle + 180}deg)`,\n                  transformOrigin: 'left center'\n                }}\n              ></div>\n            </div>\n          );\n        })}\n\n        {/* Rotating Ring Animation */}\n        <div className=\"absolute inset-0 border-4 border-dashed border-gray-300 rounded-full opacity-30 animate-spin\" style={{ animationDuration: '20s' }}></div>\n      </div>\n\n      {/* Selected Domain Info */}\n      {selectedDomain && (\n        <div className=\"mt-8 p-6 bg-white rounded-2xl shadow-lg border border-gray-200 max-w-md\">\n          <h4 className={`text-xl font-bold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--emerald-green)' }}>\n            {domains.find(d => d.id === selectedDomain)?.name[language]}\n          </h4>\n          <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`}>\n            {language === 'en' \n              ? 'Click to explore detailed specifications and implementation guidelines for this data management domain.'\n              : 'انقر لاستكشاف المواصفات التفصيلية وإرشادات التنفيذ لمجال إدارة البيانات هذا.'\n            }\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASe,SAAS,UAAU,KAA2C;QAA3C,EAAE,QAAQ,EAAE,aAAa,EAAkB,GAA3C;QAqIrB;;IApIX,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,UAAU;QACd;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAmB,IAAI;YAAiB;YACpD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAqB,IAAI;YAAiB;YACtD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAiB,IAAI;YAAiB;YAClD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAgB,IAAI;YAAiB;YACjD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAiB,IAAI;YAAgB;YACjD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAoB,IAAI;YAAiB;YACrD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAgB,IAAI;YAAgB;YAChD,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAY,IAAI;YAAmB;YAC/C,OAAO;YACP,OAAO;QACT;KACD;IAED,MAAM,oBAAoB,CAAC;QACzB,kBAAkB,OAAO,EAAE;QAC3B,cAAc,OAAO,EAAE;IACzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;gBAAM,OAAO;oBAAE,OAAO;gBAAuB;0BACxH,aAAa,OAAO,iCAAiC;;;;;;0BAGxD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAuB;kCAEjD,cAAA,6LAAC;4BAAK,WAAW,aAAa,OAAO,gBAAgB;sCAClD,aAAa,OAAO,SAAS;;;;;;;;;;;oBAKjC,QAAQ,GAAG,CAAC,CAAC,QAAQ;wBACpB,MAAM,SAAS;wBACf,MAAM,UAAU;wBAChB,MAAM,UAAU;wBAChB,MAAM,WAAW,AAAC,OAAO,KAAK,GAAG,KAAK,EAAE,GAAI;wBAC5C,MAAM,IAAI,UAAU,SAAS,KAAK,GAAG,CAAC;wBACtC,MAAM,IAAI,UAAU,SAAS,KAAK,GAAG,CAAC;wBAEtC,qBACE,6LAAC;4BAEC,WAAW,AAAC,mHAEX,OADC,mBAAmB,OAAO,EAAE,GAAG,mBAAmB;4BAEpD,OAAO;gCACL,MAAM;gCACN,KAAK;gCACL,gBAAgB,AAAC,GAAc,OAAZ,QAAQ,KAAI;4BACjC;4BACA,SAAS,IAAM,kBAAkB;;8CAEjC,6LAAC;oCACC,WAAW,AAAC,mJAEX,OADC,mBAAmB,OAAO,EAAE,GAAG,sBAAsB;oCAEvD,OAAO;wCACL,iBAAiB,OAAO,KAAK;wCAC7B,WAAW;oCACb;8CAEA,cAAA,6LAAC;wCAAK,WAAW,AAAC,6BAAmE,OAAvC,aAAa,OAAO,gBAAgB;kDAC/E,OAAO,IAAI,CAAC,SAAS;;;;;;;;;;;8CAK1B,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,OAAO,AAAC,GAAc,OAAZ,SAAS,IAAG;wCACtB,WAAW,AAAC,gCAAkD,OAAnB,OAAO,KAAK,GAAG,KAAI;wCAC9D,iBAAiB;oCACnB;;;;;;;2BAhCG,OAAO,EAAE;;;;;oBAoCpB;kCAGA,6LAAC;wBAAI,WAAU;wBAA+F,OAAO;4BAAE,mBAAmB;wBAAM;;;;;;;;;;;;YAIjJ,gCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,AAAC,0BAAoF,OAA3D,aAAa,OAAO,2BAA2B;wBAAe,OAAO;4BAAE,OAAO;wBAAuB;mCAC3I,gBAAA,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,6BAA3B,oCAAA,cAA4C,IAAI,CAAC,SAAS;;;;;;kCAE7D,6LAAC;wBAAE,WAAW,AAAC,iBAA2E,OAA3D,aAAa,OAAO,2BAA2B;kCAC3E,aAAa,OACV,4GACA;;;;;;;;;;;;;;;;;;AAOhB;GAjJwB;KAAA", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/EALayers.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface EALayersProps {\n  language: 'en' | 'ar';\n  onLayerClick: (layer: string) => void;\n}\n\nexport default function EALayers({ language, onLayerClick }: EALayersProps) {\n  const [selectedLayer, setSelectedLayer] = useState<string | null>(null);\n  const [animationPhase, setAnimationPhase] = useState(0);\n\n  const layers = [\n    {\n      id: 'business',\n      name: { en: 'Business Architecture', ar: 'هندسة الأعمال' },\n      description: { en: 'Business processes, capabilities, and organization', ar: 'العمليات التجارية والقدرات والتنظيم' },\n      color: '#026c4a',\n      icon: '🏢'\n    },\n    {\n      id: 'information',\n      name: { en: 'Information Architecture', ar: 'هندسة المعلومات' },\n      description: { en: 'Data models, information flows, and governance', ar: 'نماذج البيانات وتدفقات المعلومات والحوكمة' },\n      color: '#0c402e',\n      icon: '📊'\n    },\n    {\n      id: 'application',\n      name: { en: 'Application Architecture', ar: 'هندسة التطبيقات' },\n      description: { en: 'Software applications and their interactions', ar: 'تطبيقات البرمجيات وتفاعلاتها' },\n      color: '#026c4a',\n      icon: '💻'\n    },\n    {\n      id: 'technology',\n      name: { en: 'Technology Architecture', ar: 'هندسة التكنولوجيا' },\n      description: { en: 'Infrastructure, platforms, and technical standards', ar: 'البنية التحتية والمنصات والمعايير التقنية' },\n      color: '#0c402e',\n      icon: '⚙️'\n    }\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 2000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const handleLayerClick = (layer: any) => {\n    setSelectedLayer(layer.id);\n    onLayerClick(layer.id);\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center p-8\">\n      <h3 className={`text-2xl font-bold mb-8 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n        {language === 'en' ? 'Enterprise Architecture Layers' : 'طبقات هندسة المؤسسة'}\n      </h3>\n      \n      <div className=\"relative w-full max-w-2xl\">\n        {/* EA Layers Stack */}\n        <div className=\"space-y-4\">\n          {layers.map((layer, index) => {\n            const isActive = animationPhase === index;\n            const isSelected = selectedLayer === layer.id;\n            \n            return (\n              <div\n                key={layer.id}\n                className={`relative cursor-pointer transition-all duration-700 transform ${\n                  isActive ? 'scale-105 shadow-2xl' : 'hover:scale-102 hover:shadow-lg'\n                } ${isSelected ? 'ring-4 ring-white scale-105' : ''}`}\n                onClick={() => handleLayerClick(layer)}\n                style={{\n                  animationDelay: `${index * 0.2}s`\n                }}\n              >\n                <div \n                  className={`p-8 rounded-2xl text-white relative overflow-hidden ${\n                    isActive ? 'animate-pulse-subtle' : ''\n                  }`}\n                  style={{ backgroundColor: layer.color }}\n                >\n                  {/* Animated Background Effect */}\n                  {isActive && (\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse\"></div>\n                  )}\n                  \n                  {/* Layer Content */}\n                  <div className={`relative flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                    <div className=\"text-4xl mr-6\">{layer.icon}</div>\n                    <div className={`flex-1 ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                      <h4 className={`text-2xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {layer.name[language]}\n                      </h4>\n                      <p className={`text-white/90 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {layer.description[language]}\n                      </p>\n                    </div>\n                    \n                    {/* Layer Number */}\n                    <div className={`w-12 h-12 rounded-full bg-white/20 flex items-center justify-center font-bold text-lg ${language === 'ar' ? 'mr-6' : 'ml-6'}`}>\n                      {index + 1}\n                    </div>\n                  </div>\n\n                  {/* Connection Lines */}\n                  {index < layers.length - 1 && (\n                    <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full\">\n                      <div className=\"w-1 h-4 bg-gray-300 opacity-50\"></div>\n                      <div className=\"w-3 h-3 bg-gray-400 rounded-full transform -translate-x-1/2\"></div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Side Indicators */}\n        <div className={`absolute top-0 ${language === 'ar' ? 'left-0' : 'right-0'} transform ${language === 'ar' ? '-translate-x-16' : 'translate-x-16'} h-full flex flex-col justify-around`}>\n          {layers.map((_, index) => (\n            <div\n              key={index}\n              className={`w-3 h-3 rounded-full transition-all duration-500 ${\n                animationPhase === index ? 'bg-emerald-500 scale-150' : 'bg-gray-300'\n              }`}\n            ></div>\n          ))}\n        </div>\n      </div>\n\n      {/* Selected Layer Details */}\n      {selectedLayer && (\n        <div className=\"mt-8 p-6 bg-white rounded-2xl shadow-lg border border-gray-200 max-w-2xl\">\n          <h4 className={`text-xl font-bold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--emerald-green)' }}>\n            {layers.find(l => l.id === selectedLayer)?.name[language]}\n          </h4>\n          <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`}>\n            {language === 'en' \n              ? 'Click to explore detailed specifications, standards, and implementation guidelines for this enterprise architecture layer.'\n              : 'انقر لاستكشاف المواصفات التفصيلية والمعايير وإرشادات التنفيذ لطبقة هندسة المؤسسة هذه.'\n            }\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASe,SAAS,SAAS,KAAyC;QAAzC,EAAE,QAAQ,EAAE,YAAY,EAAiB,GAAzC;QAkIpB;;IAjIX,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,SAAS;QACb;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAAyB,IAAI;YAAgB;YACzD,aAAa;gBAAE,IAAI;gBAAsD,IAAI;YAAsC;YACnH,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA4B,IAAI;YAAkB;YAC9D,aAAa;gBAAE,IAAI;gBAAkD,IAAI;YAA4C;YACrH,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA4B,IAAI;YAAkB;YAC9D,aAAa;gBAAE,IAAI;gBAAgD,IAAI;YAA+B;YACtG,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,IAAI;gBAA2B,IAAI;YAAoB;YAC/D,aAAa;gBAAE,IAAI;gBAAsD,IAAI;YAA4C;YACzH,OAAO;YACP,MAAM;QACR;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,WAAW;+CAAY;oBAC3B;uDAAkB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI;;gBACzC;8CAAG;YACH;sCAAO,IAAM,cAAc;;QAC7B;6BAAG,EAAE;IAEL,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,MAAM,EAAE;QACzB,aAAa,MAAM,EAAE;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;gBAAM,OAAO;oBAAE,OAAO;gBAAuB;0BACxH,aAAa,OAAO,mCAAmC;;;;;;0BAG1D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,OAAO;4BAClB,MAAM,WAAW,mBAAmB;4BACpC,MAAM,aAAa,kBAAkB,MAAM,EAAE;4BAE7C,qBACE,6LAAC;gCAEC,WAAW,AAAC,iEAER,OADF,WAAW,yBAAyB,mCACrC,KAAmD,OAAhD,aAAa,gCAAgC;gCACjD,SAAS,IAAM,iBAAiB;gCAChC,OAAO;oCACL,gBAAgB,AAAC,GAAc,OAAZ,QAAQ,KAAI;gCACjC;0CAEA,cAAA,6LAAC;oCACC,WAAW,AAAC,uDAEX,OADC,WAAW,yBAAyB;oCAEtC,OAAO;wCAAE,iBAAiB,MAAM,KAAK;oCAAC;;wCAGrC,0BACC,6LAAC;4CAAI,WAAU;;;;;;sDAIjB,6LAAC;4CAAI,WAAW,AAAC,8BAAiF,OAApD,aAAa,OAAO,qBAAqB;;8DACrF,6LAAC;oDAAI,WAAU;8DAAiB,MAAM,IAAI;;;;;;8DAC1C,6LAAC;oDAAI,WAAW,AAAC,UAAwD,OAA/C,aAAa,OAAO,eAAe;;sEAC3D,6LAAC;4DAAG,WAAW,AAAC,2BAAiE,OAAvC,aAAa,OAAO,gBAAgB;sEAC3E,MAAM,IAAI,CAAC,SAAS;;;;;;sEAEvB,6LAAC;4DAAE,WAAW,AAAC,iBAAuD,OAAvC,aAAa,OAAO,gBAAgB;sEAChE,MAAM,WAAW,CAAC,SAAS;;;;;;;;;;;;8DAKhC,6LAAC;oDAAI,WAAW,AAAC,yFAA4H,OAApC,aAAa,OAAO,SAAS;8DACnI,QAAQ;;;;;;;;;;;;wCAKZ,QAAQ,OAAO,MAAM,GAAG,mBACvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;+BA1ChB,MAAM,EAAE;;;;;wBAgDnB;;;;;;kCAIF,6LAAC;wBAAI,WAAW,AAAC,kBAAuE,OAAtD,aAAa,OAAO,WAAW,WAAU,eAAsE,OAAzD,aAAa,OAAO,oBAAoB,kBAAiB;kCAC9I,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,6LAAC;gCAEC,WAAW,AAAC,oDAEX,OADC,mBAAmB,QAAQ,6BAA6B;+BAFrD;;;;;;;;;;;;;;;;YAUZ,+BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,AAAC,0BAAoF,OAA3D,aAAa,OAAO,2BAA2B;wBAAe,OAAO;4BAAE,OAAO;wBAAuB;mCAC3I,eAAA,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,4BAA1B,mCAAA,aAA0C,IAAI,CAAC,SAAS;;;;;;kCAE3D,6LAAC;wBAAE,WAAW,AAAC,iBAA2E,OAA3D,aAAa,OAAO,2BAA2B;kCAC3E,aAAa,OACV,+HACA;;;;;;;;;;;;;;;;;;AAOhB;GA9IwB;KAAA", "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/frameworks/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Hero from '../../../components/Hero';\nimport DAMAWheel from '../../../components/DAMAWheel';\nimport EALayers from '../../../components/EALayers';\n\ninterface Framework {\n  id: string;\n  name: string;\n  country: 'saudi' | 'qatar';\n  type: 'data-management' | 'enterprise-architecture';\n}\n\nconst frameworks: Framework[] = [\n  { id: 'ndmo', name: 'NDMO', country: 'saudi', type: 'data-management' },\n  { id: 'npc', name: 'NPC', country: 'qatar', type: 'data-management' },\n  { id: 'noura', name: 'NOURA', country: 'saudi', type: 'enterprise-architecture' },\n  { id: 'gea', name: 'GEA', country: 'qatar', type: 'enterprise-architecture' }\n];\n\nexport default function FrameworkManagement() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [selectedCategory, setSelectedCategory] = useState<'main' | 'data-management' | 'enterprise-architecture'>('main');\n  const [selectedFramework, setSelectedFramework] = useState<string | null>(null);\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  const content = {\n    en: {\n      title: 'Framework Management',\n      subtitle: 'Digital Transformation',\n      description: 'Manage digital transformation frameworks, methodologies, and best practices to drive organizational change.',\n      placeholder: 'Framework management functionality will be implemented here.'\n    },\n    ar: {\n      title: 'إدارة الإطار',\n      subtitle: 'التحول الرقمي',\n      description: 'إدارة أطر ومنهجيات وأفضل الممارسات للتحول الرقمي لدفع التغيير التنظيمي.',\n      placeholder: 'سيتم تنفيذ وظائف إدارة الإطار هنا.'\n    }\n  };\n\n  const frameworkIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n    </svg>\n  );\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      <Hero\n        title={content[language].title}\n        subtitle={content[language].subtitle}\n        description={content[language].description}\n        icon={frameworkIcon}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },\n          { label: content[language].title }\n        ]}\n      />\n\n      <div className=\"bg-white\">\n        {selectedCategory === 'main' && (\n          <div className=\"px-12 py-20\">\n            {/* Two Larger Enhanced Cards */}\n            <div className={`flex gap-16 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n\n              {/* Data Management Card - Enhanced */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedCategory('data-management')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-96 transition-all duration-700 ease-out transform hover:scale-105 hover:shadow-2xl\">\n                  {/* Enhanced Background with Pattern */}\n                  <div\n                    className=\"absolute inset-0 transition-all duration-700 ease-out\"\n                    style={{\n                      background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)'\n                    }}\n                  ></div>\n\n                  {/* Animated Background Pattern */}\n                  <div className=\"absolute inset-0 opacity-10\">\n                    <div className=\"absolute top-8 right-8 w-32 h-32 rounded-full bg-white animate-pulse-subtle\"></div>\n                    <div className=\"absolute bottom-8 left-8 w-24 h-24 rounded-lg bg-white rotate-45 animate-pulse-subtle\" style={{ animationDelay: '1s' }}></div>\n                    <div className=\"absolute top-1/2 left-1/3 w-16 h-16 rounded-full bg-white animate-pulse-subtle\" style={{ animationDelay: '2s' }}></div>\n                  </div>\n\n                  {/* Blade Effect - Enhanced */}\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700\">\n                    <div\n                      className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out\"\n                      style={{ width: '200%' }}\n                    ></div>\n                  </div>\n\n                  {/* Enhanced Content */}\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-12\">\n                    <div className=\"w-24 h-24 rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-500 shadow-2xl\">\n                      <svg className=\"w-12 h-12 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4\" />\n                      </svg>\n                    </div>\n                    <h3 className={`text-4xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Data Management' : 'إدارة البيانات'}\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Frameworks & Standards' : 'الأطر والمعايير'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Enterprise Architecture Card - Enhanced */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedCategory('enterprise-architecture')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-96 transition-all duration-700 ease-out transform hover:scale-105 hover:shadow-2xl\">\n                  {/* Enhanced Background with Pattern */}\n                  <div\n                    className=\"absolute inset-0 transition-all duration-700 ease-out\"\n                    style={{\n                      background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)'\n                    }}\n                  ></div>\n\n                  {/* Animated Background Pattern */}\n                  <div className=\"absolute inset-0 opacity-10\">\n                    <div className=\"absolute top-8 left-8 w-32 h-32 rounded-full bg-white animate-pulse-subtle\"></div>\n                    <div className=\"absolute bottom-8 right-8 w-24 h-24 rounded-lg bg-white rotate-45 animate-pulse-subtle\" style={{ animationDelay: '1s' }}></div>\n                    <div className=\"absolute top-1/2 right-1/3 w-16 h-16 rounded-full bg-white animate-pulse-subtle\" style={{ animationDelay: '2s' }}></div>\n                  </div>\n\n                  {/* Blade Effect - Enhanced */}\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700\">\n                    <div\n                      className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out\"\n                      style={{ width: '200%' }}\n                    ></div>\n                  </div>\n\n                  {/* Enhanced Content */}\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-12\">\n                    <div className=\"w-24 h-24 rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-500 shadow-2xl\">\n                      <svg className=\"w-12 h-12 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                      </svg>\n                    </div>\n                    <h3 className={`text-4xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Enterprise Architecture' : 'هندسة المؤسسة'}\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Frameworks & Models' : 'الأطر والنماذج'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Data Management Frameworks */}\n        {selectedCategory === 'data-management' && (\n          <div className=\"px-12 py-16\">\n            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <button\n                onClick={() => setSelectedCategory('main')}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />\n                </svg>\n              </button>\n              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {language === 'en' ? 'Data Management Frameworks' : 'أطر إدارة البيانات'}\n              </h2>\n            </div>\n\n            <div className={`flex gap-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              {/* NDMO Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedFramework('ndmo')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)' }}\n                  ></div>\n\n                  {/* Saudi Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-green-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇸🇦</span>\n                    <span>{language === 'en' ? 'Saudi Arabia' : 'السعودية'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      NDMO\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'National Data Management Office' : 'مكتب إدارة البيانات الوطني'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* NPC Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedFramework('npc')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)' }}\n                  ></div>\n\n                  {/* Qatar Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-red-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇶🇦</span>\n                    <span>{language === 'en' ? 'Qatar' : 'قطر'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      NPC\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'National Planning Council' : 'مجلس التخطيط الوطني'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Enterprise Architecture Frameworks */}\n        {selectedCategory === 'enterprise-architecture' && (\n          <div className=\"px-12 py-16\">\n            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <button\n                onClick={() => setSelectedCategory('main')}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />\n                </svg>\n              </button>\n              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {language === 'en' ? 'Enterprise Architecture Frameworks' : 'أطر هندسة المؤسسة'}\n              </h2>\n            </div>\n\n            <div className={`flex gap-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              {/* NOURA Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedFramework('noura')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)' }}\n                  ></div>\n\n                  {/* Saudi Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-green-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇸🇦</span>\n                    <span>{language === 'en' ? 'Saudi Arabia' : 'السعودية'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      NOURA\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'National Enterprise Architecture' : 'هندسة المؤسسة الوطنية'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* GEA Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedFramework('gea')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)' }}\n                  ></div>\n\n                  {/* Qatar Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-red-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇶🇦</span>\n                    <span>{language === 'en' ? 'Qatar' : 'قطر'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      GEA\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Government Enterprise Architecture' : 'هندسة المؤسسة الحكومية'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Framework Detail Views */}\n        {selectedFramework && (\n          <div className=\"px-12 py-16\">\n            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <button\n                onClick={() => setSelectedFramework(null)}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />\n                </svg>\n              </button>\n              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {selectedFramework.toUpperCase()}\n              </h2>\n\n              {/* Country Tag */}\n              <div className={`px-3 py-1 rounded-full text-white text-sm font-bold flex items-center gap-2 ${language === 'ar' ? 'mr-4' : 'ml-4'}`}\n                style={{\n                  backgroundColor: frameworks.find(f => f.id === selectedFramework)?.country === 'saudi' ? '#16a34a' : '#dc2626'\n                }}\n              >\n                <span>{frameworks.find(f => f.id === selectedFramework)?.country === 'saudi' ? '🇸🇦' : '🇶🇦'}</span>\n                <span>\n                  {frameworks.find(f => f.id === selectedFramework)?.country === 'saudi'\n                    ? (language === 'en' ? 'Saudi Arabia' : 'السعودية')\n                    : (language === 'en' ? 'Qatar' : 'قطر')\n                  }\n                </span>\n              </div>\n            </div>\n\n            {/* DAMA Wheel for Data Management Frameworks */}\n            {(selectedFramework === 'ndmo' || selectedFramework === 'npc') && (\n              <div className=\"bg-white rounded-3xl shadow-lg p-8\">\n                <DAMAWheel\n                  language={language}\n                  onDomainClick={(domain) => {\n                    console.log(`Clicked domain: ${domain} in framework: ${selectedFramework}`);\n                  }}\n                />\n              </div>\n            )}\n\n            {/* EA Layers for Enterprise Architecture Frameworks */}\n            {(selectedFramework === 'noura' || selectedFramework === 'gea') && (\n              <div className=\"bg-white rounded-3xl shadow-lg p-8\">\n                <EALayers\n                  language={language}\n                  onLayerClick={(layer) => {\n                    console.log(`Clicked layer: ${layer} in framework: ${selectedFramework}`);\n                  }}\n                />\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAcA,MAAM,aAA0B;IAC9B;QAAE,IAAI;QAAQ,MAAM;QAAQ,SAAS;QAAS,MAAM;IAAkB;IACtE;QAAE,IAAI;QAAO,MAAM;QAAO,SAAS;QAAS,MAAM;IAAkB;IACpE;QAAE,IAAI;QAAS,MAAM;QAAS,SAAS;QAAS,MAAM;IAA0B;IAChF;QAAE,IAAI;QAAO,MAAM;QAAO,SAAS;QAAS,MAAM;IAA0B;CAC7E;AAEc,SAAS;QA+SW,kBAGZ,mBAEJ;;IAnTjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0D;IACjH,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;YACxE,YAAY;QACd;wCAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;IACF;IAEA,MAAM,8BACJ,6LAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,6LAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,qBACE,6LAAC;QAAI,WAAW,AAAC,GAAiD,OAA/C,aAAa,OAAO,eAAe;;0BACpD,6LAAC,6HAAA,CAAA,UAAI;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;gBAC9B,UAAU,OAAO,CAAC,SAAS,CAAC,QAAQ;gBACpC,aAAa,OAAO,CAAC,SAAS,CAAC,WAAW;gBAC1C,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;wBAAe,MAAM;oBAAa;oBAC7E;wBAAE,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;oBAAC;iBAClC;;;;;;0BAGH,6LAAC;gBAAI,WAAU;;oBACZ,qBAAqB,wBACpB,6LAAC;wBAAI,WAAU;kCAEb,cAAA,6LAAC;4BAAI,WAAW,AAAC,eAAkE,OAApD,aAAa,OAAO,qBAAqB;;8CAGtE,6LAAC;oCAAI,WAAU;oCAA8B,SAAS,IAAM,oBAAoB;8CAC9E,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,YAAY;gDACd;;;;;;0DAIF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;wDAAwF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;kEACrI,6LAAC;wDAAI,WAAU;wDAAiF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;;;;;;;0DAIhI,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAAO;;;;;;;;;;;0DAK3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;wDAAG,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;kEACtF,aAAa,OAAO,oBAAoB;;;;;;kEAE3C,6LAAC;wDAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kEACxE,aAAa,OAAO,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;8CAOxD,6LAAC;oCAAI,WAAU;oCAA8B,SAAS,IAAM,oBAAoB;8CAC9E,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,YAAY;gDACd;;;;;;0DAIF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;wDAAyF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;kEACtI,6LAAC;wDAAI,WAAU;wDAAkF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;;;;;;;0DAIjI,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAAO;;;;;;;;;;;0DAK3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;wDAAG,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;kEACtF,aAAa,OAAO,4BAA4B;;;;;;kEAEnD,6LAAC;wDAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;kEACxE,aAAa,OAAO,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU1D,qBAAqB,mCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,AAAC,0BAA6E,OAApD,aAAa,OAAO,qBAAqB;;kDACjF,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;kDAEvC,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAG,aAAa,OAAO,iBAAiB;;;;;;;;;;;;;;;;kDAG/G,6LAAC;wCAAG,WAAW,AAAC,sBAAqE,OAAhD,aAAa,OAAO,qBAAqB;wCAAU,OAAO;4CAAE,OAAO;wCAAuB;kDAC5H,aAAa,OAAO,+BAA+B;;;;;;;;;;;;0CAIxD,6LAAC;gCAAI,WAAW,AAAC,eAAkE,OAApD,aAAa,OAAO,qBAAqB;;kDAEtE,6LAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,qBAAqB;kDAC/E,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAM,aAAa,OAAO,iBAAiB;;;;;;;;;;;;8DAG9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;sEAAM;;;;;;sEAG/F,6LAAC;4DAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;sEACxE,aAAa,OAAO,oCAAoC;;;;;;;;;;;;;;;;;;;;;;;kDAOjE,6LAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,qBAAqB;kDAC/E,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAM,aAAa,OAAO,UAAU;;;;;;;;;;;;8DAGvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;sEAAM;;;;;;sEAG/F,6LAAC;4DAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;sEACxE,aAAa,OAAO,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUhE,qBAAqB,2CACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,AAAC,0BAA6E,OAApD,aAAa,OAAO,qBAAqB;;kDACjF,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;kDAEvC,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAG,aAAa,OAAO,iBAAiB;;;;;;;;;;;;;;;;kDAG/G,6LAAC;wCAAG,WAAW,AAAC,sBAAqE,OAAhD,aAAa,OAAO,qBAAqB;wCAAU,OAAO;4CAAE,OAAO;wCAAuB;kDAC5H,aAAa,OAAO,uCAAuC;;;;;;;;;;;;0CAIhE,6LAAC;gCAAI,WAAW,AAAC,eAAkE,OAApD,aAAa,OAAO,qBAAqB;;kDAEtE,6LAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,qBAAqB;kDAC/E,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAM,aAAa,OAAO,iBAAiB;;;;;;;;;;;;8DAG9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;sEAAM;;;;;;sEAG/F,6LAAC;4DAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;sEACxE,aAAa,OAAO,qCAAqC;;;;;;;;;;;;;;;;;;;;;;;kDAOlE,6LAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,qBAAqB;kDAC/E,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAM,aAAa,OAAO,UAAU;;;;;;;;;;;;8DAGvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,AAAC,sCAA4E,OAAvC,aAAa,OAAO,gBAAgB;sEAAM;;;;;;sEAG/F,6LAAC;4DAAE,WAAW,AAAC,yBAA+D,OAAvC,aAAa,OAAO,gBAAgB;sEACxE,aAAa,OAAO,uCAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUzE,mCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,AAAC,0BAA6E,OAApD,aAAa,OAAO,qBAAqB;;kDACjF,6LAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;kDAEvC,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAG,aAAa,OAAO,iBAAiB;;;;;;;;;;;;;;;;kDAG/G,6LAAC;wCAAG,WAAW,AAAC,sBAAqE,OAAhD,aAAa,OAAO,qBAAqB;wCAAU,OAAO;4CAAE,OAAO;wCAAuB;kDAC5H,kBAAkB,WAAW;;;;;;kDAIhC,6LAAC;wCAAI,WAAW,AAAC,+EAAkH,OAApC,aAAa,OAAO,SAAS;wCAC1H,OAAO;4CACL,iBAAiB,EAAA,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gCAA9B,uCAAA,iBAAkD,OAAO,MAAK,UAAU,YAAY;wCACvG;;0DAEA,6LAAC;0DAAM,EAAA,oBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gCAA9B,wCAAA,kBAAkD,OAAO,MAAK,UAAU,SAAS;;;;;;0DACxF,6LAAC;0DACE,EAAA,oBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gCAA9B,wCAAA,kBAAkD,OAAO,MAAK,UAC1D,aAAa,OAAO,iBAAiB,aACrC,aAAa,OAAO,UAAU;;;;;;;;;;;;;;;;;;4BAOxC,CAAC,sBAAsB,UAAU,sBAAsB,KAAK,mBAC3D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,kIAAA,CAAA,UAAS;oCACR,UAAU;oCACV,eAAe,CAAC;wCACd,QAAQ,GAAG,CAAC,AAAC,mBAA0C,OAAxB,QAAO,mBAAmC,OAAlB;oCACzD;;;;;;;;;;;4BAML,CAAC,sBAAsB,WAAW,sBAAsB,KAAK,mBAC5D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iIAAA,CAAA,UAAQ;oCACP,UAAU;oCACV,cAAc,CAAC;wCACb,QAAQ,GAAG,CAAC,AAAC,kBAAwC,OAAvB,OAAM,mBAAmC,OAAlB;oCACvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAxVwB;KAAA", "debugId": null}}]}