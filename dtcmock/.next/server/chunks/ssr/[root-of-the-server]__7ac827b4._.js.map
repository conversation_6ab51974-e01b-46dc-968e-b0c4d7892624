{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\n\ninterface SidebarProps {\n  userRole: 'admin' | 'consultant' | 'project-manager' | 'trainee';\n  language: 'en' | 'ar';\n  onLanguageChange: (lang: 'en' | 'ar') => void;\n}\n\nexport default function Sidebar({ userRole, language, onLanguageChange }: SidebarProps) {\n  const router = useRouter();\n  const [isCollapsed, setIsCollapsed] = useState(false);\n\n  const content = {\n    en: {\n      home: 'Home',\n      userManagement: 'User Management',\n      frameworkManagement: 'Framework Management',\n      projects: 'Projects',\n      trainingCourses: 'Training Courses',\n      logout: 'Logout',\n      profile: 'Profile',\n      toggleSidebar: 'Toggle Sidebar'\n    },\n    ar: {\n      home: 'الرئيسية',\n      userManagement: 'إدارة المستخدمين',\n      frameworkManagement: 'إدارة الإطار',\n      projects: 'المشاريع',\n      trainingCourses: 'الدورات التدريبية',\n      logout: 'تسجيل الخروج',\n      profile: 'الملف الشخصي',\n      toggleSidebar: 'تبديل الشريط الجانبي'\n    }\n  };\n\n  const adminMenuItems = [\n    { key: 'home', label: content[language].home, href: '/dashboard', icon: '🏠' },\n    { key: 'userManagement', label: content[language].userManagement, href: '/dashboard/users', icon: '👥' },\n    { key: 'frameworkManagement', label: content[language].frameworkManagement, href: '/dashboard/frameworks', icon: '🏗️' },\n    { key: 'projects', label: content[language].projects, href: '/dashboard/projects', icon: '📊' },\n    { key: 'trainingCourses', label: content[language].trainingCourses, href: '/dashboard/training', icon: '🎓' }\n  ];\n\n  const otherRoleMenuItems = [\n    { key: 'home', label: content[language].home, href: '/dashboard', icon: '🏠' }\n  ];\n\n  const menuItems = userRole === 'admin' ? adminMenuItems : otherRoleMenuItems;\n\n  const handleLogout = () => {\n    // Mock logout - redirect to login\n    router.push('/login');\n  };\n\n  return (\n    <div \n      className={`fixed top-0 h-full bg-white shadow-lg transition-all duration-300 z-50 ${\n        isCollapsed ? 'w-16' : 'w-64'\n      } ${language === 'ar' ? 'right-0' : 'left-0'}`}\n      dir={language === 'ar' ? 'rtl' : 'ltr'}\n      style={{ borderColor: 'var(--charcoal-grey)' }}\n    >\n      {/* Header with Profile */}\n      <div className=\"p-4 border-b\" style={{ borderColor: 'var(--charcoal-grey)' }}>\n        <div className=\"flex items-center justify-between\">\n          {!isCollapsed && (\n            <div className=\"flex items-center space-x-3\">\n              {/* Profile Picture */}\n              <div \n                className=\"w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg\"\n                style={{ backgroundColor: 'var(--emerald-green)' }}\n              >\n                👤\n              </div>\n              <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                <p className={`font-medium text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {content[language].profile}\n                </p>\n                <p className={`text-xs opacity-70 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {userRole.charAt(0).toUpperCase() + userRole.slice(1).replace('-', ' ')}\n                </p>\n              </div>\n            </div>\n          )}\n          \n          {/* Toggle Button */}\n          <button\n            onClick={() => setIsCollapsed(!isCollapsed)}\n            className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n            title={content[language].toggleSidebar}\n          >\n            <span className=\"text-lg\">\n              {isCollapsed ? '→' : (language === 'ar' ? '←' : '→')}\n            </span>\n          </button>\n        </div>\n      </div>\n\n      {/* Navigation Menu */}\n      <nav className=\"flex-1 p-4\">\n        <ul className=\"space-y-2\">\n          {menuItems.map((item) => (\n            <li key={item.key}>\n              <Link\n                href={item.href}\n                className={`nav-item flex items-center p-3 rounded-lg group ${\n                  language === 'ar' ? 'flex-row-reverse' : 'flex-row'\n                }`}\n                style={{ color: 'var(--charcoal-grey)' }}\n              >\n                <span className=\"text-lg\">{item.icon}</span>\n                {!isCollapsed && (\n                  <span \n                    className={`font-medium ${language === 'ar' ? 'font-arabic mr-3' : 'ml-3'}`}\n                  >\n                    {item.label}\n                  </span>\n                )}\n              </Link>\n            </li>\n          ))}\n        </ul>\n      </nav>\n\n      {/* Footer with Language Toggle and Logout */}\n      <div className=\"p-4 border-t\" style={{ borderColor: 'var(--charcoal-grey)' }}>\n        {!isCollapsed && (\n          <div className=\"space-y-3\">\n            {/* Language Toggle */}\n            <button\n              onClick={() => onLanguageChange(language === 'en' ? 'ar' : 'en')}\n              className={`sidebar-button w-full flex items-center p-2 rounded-lg hover:bg-gray-100 ${\n                language === 'ar' ? 'flex-row-reverse' : 'flex-row'\n              }`}\n              style={{ color: 'var(--charcoal-grey)' }}\n            >\n              <span className=\"text-lg\">🌐</span>\n              <span className={`font-medium ${language === 'ar' ? 'font-arabic mr-3' : 'ml-3'}`}>\n                {language === 'en' ? 'العربية' : 'English'}\n              </span>\n            </button>\n\n            {/* Logout Button */}\n            <button\n              onClick={handleLogout}\n              className={`sidebar-button w-full flex items-center p-2 rounded-lg hover:bg-red-50 text-red-600 ${\n                language === 'ar' ? 'flex-row-reverse' : 'flex-row'\n              }`}\n            >\n              <span className=\"text-lg\">🚪</span>\n              <span className={`font-medium ${language === 'ar' ? 'font-arabic mr-3' : 'ml-3'}`}>\n                {content[language].logout}\n              </span>\n            </button>\n          </div>\n        )}\n        \n        {isCollapsed && (\n          <div className=\"space-y-3 flex flex-col items-center\">\n            {/* Language Toggle - Collapsed */}\n            <button\n              onClick={() => onLanguageChange(language === 'en' ? 'ar' : 'en')}\n              className=\"p-2 rounded-lg transition-colors hover:bg-gray-100\"\n              style={{ color: 'var(--charcoal-grey)' }}\n              title={language === 'en' ? 'العربية' : 'English'}\n            >\n              <span className=\"text-lg\">🌐</span>\n            </button>\n\n            {/* Logout Button - Collapsed */}\n            <button\n              onClick={handleLogout}\n              className=\"p-2 rounded-lg transition-colors hover:bg-red-50 text-red-600\"\n              title={content[language].logout}\n            >\n              <span className=\"text-lg\">🚪</span>\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYe,SAAS,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAgB;IACpF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,UAAU;QACd,IAAI;YACF,MAAM;YACN,gBAAgB;YAChB,qBAAqB;YACrB,UAAU;YACV,iBAAiB;YACjB,QAAQ;YACR,SAAS;YACT,eAAe;QACjB;QACA,IAAI;YACF,MAAM;YACN,gBAAgB;YAChB,qBAAqB;YACrB,UAAU;YACV,iBAAiB;YACjB,QAAQ;YACR,SAAS;YACT,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB;QACrB;YAAE,KAAK;YAAQ,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI;YAAE,MAAM;YAAc,MAAM;QAAK;QAC7E;YAAE,KAAK;YAAkB,OAAO,OAAO,CAAC,SAAS,CAAC,cAAc;YAAE,MAAM;YAAoB,MAAM;QAAK;QACvG;YAAE,KAAK;YAAuB,OAAO,OAAO,CAAC,SAAS,CAAC,mBAAmB;YAAE,MAAM;YAAyB,MAAM;QAAM;QACvH;YAAE,KAAK;YAAY,OAAO,OAAO,CAAC,SAAS,CAAC,QAAQ;YAAE,MAAM;YAAuB,MAAM;QAAK;QAC9F;YAAE,KAAK;YAAmB,OAAO,OAAO,CAAC,SAAS,CAAC,eAAe;YAAE,MAAM;YAAuB,MAAM;QAAK;KAC7G;IAED,MAAM,qBAAqB;QACzB;YAAE,KAAK;YAAQ,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI;YAAE,MAAM;YAAc,MAAM;QAAK;KAC9E;IAED,MAAM,YAAY,aAAa,UAAU,iBAAiB;IAE1D,MAAM,eAAe;QACnB,kCAAkC;QAClC,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,uEAAuE,EACjF,cAAc,SAAS,OACxB,CAAC,EAAE,aAAa,OAAO,YAAY,UAAU;QAC9C,KAAK,aAAa,OAAO,QAAQ;QACjC,OAAO;YAAE,aAAa;QAAuB;;0BAG7C,8OAAC;gBAAI,WAAU;gBAAe,OAAO;oBAAE,aAAa;gBAAuB;0BACzE,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,CAAC,6BACA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB;oCAAuB;8CAClD;;;;;;8CAGD,8OAAC;oCAAI,WAAW,GAAG,aAAa,OAAO,eAAe,aAAa;;sDACjE,8OAAC;4CAAE,WAAW,CAAC,oBAAoB,EAAE,aAAa,OAAO,gBAAgB,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAuB;sDACnH,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;sDAE5B,8OAAC;4CAAE,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,gBAAgB,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAuB;sDAClH,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAO3E,8OAAC;4BACC,SAAS,IAAM,eAAe,CAAC;4BAC/B,WAAU;4BACV,OAAO,OAAO,CAAC,SAAS,CAAC,aAAa;sCAEtC,cAAA,8OAAC;gCAAK,WAAU;0CACb,cAAc,MAAO,aAAa,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAOxD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,gDAAgD,EAC1D,aAAa,OAAO,qBAAqB,YACzC;gCACF,OAAO;oCAAE,OAAO;gCAAuB;;kDAEvC,8OAAC;wCAAK,WAAU;kDAAW,KAAK,IAAI;;;;;;oCACnC,CAAC,6BACA,8OAAC;wCACC,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,qBAAqB,QAAQ;kDAE1E,KAAK,KAAK;;;;;;;;;;;;2BAbV,KAAK,GAAG;;;;;;;;;;;;;;;0BAuBvB,8OAAC;gBAAI,WAAU;gBAAe,OAAO;oBAAE,aAAa;gBAAuB;;oBACxE,CAAC,6BACA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,SAAS,IAAM,iBAAiB,aAAa,OAAO,OAAO;gCAC3D,WAAW,CAAC,yEAAyE,EACnF,aAAa,OAAO,qBAAqB,YACzC;gCACF,OAAO;oCAAE,OAAO;gCAAuB;;kDAEvC,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAK,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,qBAAqB,QAAQ;kDAC9E,aAAa,OAAO,YAAY;;;;;;;;;;;;0CAKrC,8OAAC;gCACC,SAAS;gCACT,WAAW,CAAC,oFAAoF,EAC9F,aAAa,OAAO,qBAAqB,YACzC;;kDAEF,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAK,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,qBAAqB,QAAQ;kDAC9E,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;;;;;;;;;;;;;oBAMhC,6BACC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,SAAS,IAAM,iBAAiB,aAAa,OAAO,OAAO;gCAC3D,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAuB;gCACvC,OAAO,aAAa,OAAO,YAAY;0CAEvC,cAAA,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;0CAI5B,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM;0CAE/B,cAAA,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Sidebar from '../../components/Sidebar';\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const [userRole, setUserRole] = useState<'admin' | 'consultant' | 'project-manager' | 'trainee'>('admin');\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  // Mock: Get user data from localStorage or context\n  useEffect(() => {\n    // In a real app, this would come from authentication context or API\n    const mockUserRole = localStorage.getItem('userRole') as 'admin' | 'consultant' | 'project-manager' | 'trainee' || 'admin';\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    \n    setUserRole(mockUserRole);\n    setLanguage(mockLanguage);\n  }, []);\n\n  const handleLanguageChange = (newLanguage: 'en' | 'ar') => {\n    setLanguage(newLanguage);\n    localStorage.setItem('language', newLanguage);\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${language === 'ar' ? 'rtl' : 'ltr'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>\n      {/* Sidebar */}\n      <Sidebar \n        userRole={userRole} \n        language={language} \n        onLanguageChange={handleLanguageChange}\n      />\n      \n      {/* Main Content */}\n      <div className={`sidebar-transition ${language === 'ar' ? 'mr-64' : 'ml-64'}`}>\n        <main className=\"p-6 dashboard-content\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0D;IACjG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oEAAoE;QACpE,MAAM,eAAe,aAAa,OAAO,CAAC,eAAyE;QACnH,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QAExE,YAAY;QACZ,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,aAAa,OAAO,CAAC,YAAY;IACnC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,QAAQ,OAAO;QAAE,KAAK,aAAa,OAAO,QAAQ;;0BAE/G,8OAAC,6HAAA,CAAA,UAAO;gBACN,UAAU;gBACV,UAAU;gBACV,kBAAkB;;;;;;0BAIpB,8OAAC;gBAAI,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,UAAU,SAAS;0BAC3E,cAAA,8OAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}