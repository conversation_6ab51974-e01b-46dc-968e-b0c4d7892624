{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\n\ninterface SidebarProps {\n  userRole: 'admin' | 'consultant' | 'project-manager' | 'trainee';\n  language: 'en' | 'ar';\n  onLanguageChange: (lang: 'en' | 'ar') => void;\n}\n\n// Professional SVG Icons\nconst Icons = {\n  home: (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n    </svg>\n  ),\n  users: (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n    </svg>\n  ),\n  framework: (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n    </svg>\n  ),\n  projects: (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n    </svg>\n  ),\n  training: (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n    </svg>\n  ),\n  language: (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129\" />\n    </svg>\n  ),\n  logout: (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n    </svg>\n  ),\n  menu: (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n    </svg>\n  ),\n  close: (\n    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n    </svg>\n  )\n};\n\nexport default function Sidebar({ userRole, language, onLanguageChange }: SidebarProps) {\n  const router = useRouter();\n  const [isCollapsed, setIsCollapsed] = useState(false);\n\n  const content = {\n    en: {\n      home: 'Home',\n      userManagement: 'User Management',\n      frameworkManagement: 'Framework Management',\n      projects: 'Projects',\n      trainingCourses: 'Training Courses',\n      logout: 'Logout',\n      profile: 'Profile',\n      toggleSidebar: 'Toggle Sidebar'\n    },\n    ar: {\n      home: 'الرئيسية',\n      userManagement: 'إدارة المستخدمين',\n      frameworkManagement: 'إدارة الإطار',\n      projects: 'المشاريع',\n      trainingCourses: 'الدورات التدريبية',\n      logout: 'تسجيل الخروج',\n      profile: 'الملف الشخصي',\n      toggleSidebar: 'تبديل الشريط الجانبي'\n    }\n  };\n\n  const adminMenuItems = [\n    { key: 'home', label: content[language].home, href: '/dashboard', icon: Icons.home },\n    { key: 'userManagement', label: content[language].userManagement, href: '/dashboard/users', icon: Icons.users },\n    { key: 'frameworkManagement', label: content[language].frameworkManagement, href: '/dashboard/frameworks', icon: Icons.framework },\n    { key: 'projects', label: content[language].projects, href: '/dashboard/projects', icon: Icons.projects },\n    { key: 'trainingCourses', label: content[language].trainingCourses, href: '/dashboard/training', icon: Icons.training }\n  ];\n\n  const otherRoleMenuItems = [\n    { key: 'home', label: content[language].home, href: '/dashboard', icon: Icons.home }\n  ];\n\n  const menuItems = userRole === 'admin' ? adminMenuItems : otherRoleMenuItems;\n\n  const handleLogout = () => {\n    // Mock logout - redirect to login\n    router.push('/login');\n  };\n\n  return (\n    <div\n      className={`fixed top-0 h-full transition-all duration-300 z-50 ${\n        isCollapsed ? 'w-16' : 'w-72'\n      } ${language === 'ar' ? 'right-0' : 'left-0'}`}\n      dir={language === 'ar' ? 'rtl' : 'ltr'}\n      style={{\n        background: 'linear-gradient(180deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n        boxShadow: '4px 0 20px rgba(0, 0, 0, 0.1)'\n      }}\n    >\n      {/* Header with Profile */}\n      <div className=\"p-6 border-b border-white/10\">\n        <div className=\"flex items-center justify-between\">\n          {!isCollapsed && (\n            <div className={`flex items-center ${language === 'ar' ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>\n              {/* Professional Profile Picture */}\n              <div className=\"relative\">\n                <div className=\"w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30\">\n                  <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n                <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white\"></div>\n              </div>\n              <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                <p className={`font-semibold text-sm text-white ${language === 'ar' ? 'font-arabic' : ''}`}>\n                  {content[language].profile}\n                </p>\n                <p className={`text-xs text-white/70 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                  {userRole.charAt(0).toUpperCase() + userRole.slice(1).replace('-', ' ')}\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Modern Toggle Button */}\n          <button\n            onClick={() => setIsCollapsed(!isCollapsed)}\n            className=\"p-2 rounded-lg hover:bg-white/10 transition-all duration-200 text-white\"\n            title={content[language].toggleSidebar}\n          >\n            {isCollapsed ? Icons.menu : Icons.close}\n          </button>\n        </div>\n      </div>\n\n      {/* Navigation Menu */}\n      <nav className=\"flex-1 px-4 py-6\">\n        <ul className=\"space-y-1\">\n          {menuItems.map((item) => (\n            <li key={item.key}>\n              <Link\n                href={item.href}\n                className={`group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-white/10 hover:translate-x-1 ${\n                  language === 'ar' ? 'flex-row-reverse hover:-translate-x-1' : 'flex-row'\n                } text-white/90 hover:text-white`}\n              >\n                <span className=\"flex-shrink-0\">{item.icon}</span>\n                {!isCollapsed && (\n                  <span\n                    className={`${language === 'ar' ? 'font-arabic mr-4' : 'ml-4'} transition-all duration-200`}\n                  >\n                    {item.label}\n                  </span>\n                )}\n              </Link>\n            </li>\n          ))}\n        </ul>\n      </nav>\n\n      {/* Footer with Language Toggle and Logout */}\n      <div className=\"p-4 border-t border-white/10 mt-auto\">\n        {!isCollapsed && (\n          <div className=\"space-y-2\">\n            {/* Language Toggle */}\n            <button\n              onClick={() => onLanguageChange(language === 'en' ? 'ar' : 'en')}\n              className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-white/10 text-white/90 hover:text-white ${\n                language === 'ar' ? 'flex-row-reverse' : 'flex-row'\n              }`}\n            >\n              <span className=\"flex-shrink-0\">{Icons.language}</span>\n              <span className={`${language === 'ar' ? 'font-arabic mr-4' : 'ml-4'}`}>\n                {language === 'en' ? 'العربية' : 'English'}\n              </span>\n            </button>\n\n            {/* Logout Button */}\n            <button\n              onClick={handleLogout}\n              className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-red-500/20 text-red-200 hover:text-red-100 ${\n                language === 'ar' ? 'flex-row-reverse' : 'flex-row'\n              }`}\n            >\n              <span className=\"flex-shrink-0\">{Icons.logout}</span>\n              <span className={`${language === 'ar' ? 'font-arabic mr-4' : 'ml-4'}`}>\n                {content[language].logout}\n              </span>\n            </button>\n          </div>\n        )}\n\n        {isCollapsed && (\n          <div className=\"space-y-2 flex flex-col items-center\">\n            {/* Language Toggle - Collapsed */}\n            <button\n              onClick={() => onLanguageChange(language === 'en' ? 'ar' : 'en')}\n              className=\"p-3 rounded-xl transition-all duration-200 hover:bg-white/10 text-white/90 hover:text-white\"\n              title={language === 'en' ? 'العربية' : 'English'}\n            >\n              {Icons.language}\n            </button>\n\n            {/* Logout Button - Collapsed */}\n            <button\n              onClick={handleLogout}\n              className=\"p-3 rounded-xl transition-all duration-200 hover:bg-red-500/20 text-red-200 hover:text-red-100\"\n              title={content[language].logout}\n            >\n              {Icons.logout}\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYA,yBAAyB;AACzB,MAAM,QAAQ;IACZ,oBACE,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAGzE,qBACE,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAGzE,yBACE,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAGzE,wBACE,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAGzE,wBACE,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAGzE,wBACE,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAGzE,sBACE,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAGzE,oBACE,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAGzE,qBACE,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;AAG3E;AAEe,SAAS,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAgB;IACpF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,UAAU;QACd,IAAI;YACF,MAAM;YACN,gBAAgB;YAChB,qBAAqB;YACrB,UAAU;YACV,iBAAiB;YACjB,QAAQ;YACR,SAAS;YACT,eAAe;QACjB;QACA,IAAI;YACF,MAAM;YACN,gBAAgB;YAChB,qBAAqB;YACrB,UAAU;YACV,iBAAiB;YACjB,QAAQ;YACR,SAAS;YACT,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB;QACrB;YAAE,KAAK;YAAQ,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI;YAAE,MAAM;YAAc,MAAM,MAAM,IAAI;QAAC;QACnF;YAAE,KAAK;YAAkB,OAAO,OAAO,CAAC,SAAS,CAAC,cAAc;YAAE,MAAM;YAAoB,MAAM,MAAM,KAAK;QAAC;QAC9G;YAAE,KAAK;YAAuB,OAAO,OAAO,CAAC,SAAS,CAAC,mBAAmB;YAAE,MAAM;YAAyB,MAAM,MAAM,SAAS;QAAC;QACjI;YAAE,KAAK;YAAY,OAAO,OAAO,CAAC,SAAS,CAAC,QAAQ;YAAE,MAAM;YAAuB,MAAM,MAAM,QAAQ;QAAC;QACxG;YAAE,KAAK;YAAmB,OAAO,OAAO,CAAC,SAAS,CAAC,eAAe;YAAE,MAAM;YAAuB,MAAM,MAAM,QAAQ;QAAC;KACvH;IAED,MAAM,qBAAqB;QACzB;YAAE,KAAK;YAAQ,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI;YAAE,MAAM;YAAc,MAAM,MAAM,IAAI;QAAC;KACpF;IAED,MAAM,YAAY,aAAa,UAAU,iBAAiB;IAE1D,MAAM,eAAe;QACnB,kCAAkC;QAClC,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,oDAAoD,EAC9D,cAAc,SAAS,OACxB,CAAC,EAAE,aAAa,OAAO,YAAY,UAAU;QAC9C,KAAK,aAAa,OAAO,QAAQ;QACjC,OAAO;YACL,YAAY;YACZ,WAAW;QACb;;0BAGA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,CAAC,6BACA,8OAAC;4BAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,8BAA8B,aAAa;;8CAElG,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAW,GAAG,aAAa,OAAO,eAAe,aAAa;;sDACjE,8OAAC;4CAAE,WAAW,CAAC,iCAAiC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sDACvF,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;sDAE5B,8OAAC;4CAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;sDAC5E,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAO3E,8OAAC;4BACC,SAAS,IAAM,eAAe,CAAC;4BAC/B,WAAU;4BACV,OAAO,OAAO,CAAC,SAAS,CAAC,aAAa;sCAErC,cAAc,MAAM,IAAI,GAAG,MAAM,KAAK;;;;;;;;;;;;;;;;;0BAM7C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,mIAAmI,EAC7I,aAAa,OAAO,0CAA0C,WAC/D,+BAA+B,CAAC;;kDAEjC,8OAAC;wCAAK,WAAU;kDAAiB,KAAK,IAAI;;;;;;oCACzC,CAAC,6BACA,8OAAC;wCACC,WAAW,GAAG,aAAa,OAAO,qBAAqB,OAAO,4BAA4B,CAAC;kDAE1F,KAAK,KAAK;;;;;;;;;;;;2BAZV,KAAK,GAAG;;;;;;;;;;;;;;;0BAsBvB,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,6BACA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,SAAS,IAAM,iBAAiB,aAAa,OAAO,OAAO;gCAC3D,WAAW,CAAC,+IAA+I,EACzJ,aAAa,OAAO,qBAAqB,YACzC;;kDAEF,8OAAC;wCAAK,WAAU;kDAAiB,MAAM,QAAQ;;;;;;kDAC/C,8OAAC;wCAAK,WAAW,GAAG,aAAa,OAAO,qBAAqB,QAAQ;kDAClE,aAAa,OAAO,YAAY;;;;;;;;;;;;0CAKrC,8OAAC;gCACC,SAAS;gCACT,WAAW,CAAC,kJAAkJ,EAC5J,aAAa,OAAO,qBAAqB,YACzC;;kDAEF,8OAAC;wCAAK,WAAU;kDAAiB,MAAM,MAAM;;;;;;kDAC7C,8OAAC;wCAAK,WAAW,GAAG,aAAa,OAAO,qBAAqB,QAAQ;kDAClE,OAAO,CAAC,SAAS,CAAC,MAAM;;;;;;;;;;;;;;;;;;oBAMhC,6BACC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,SAAS,IAAM,iBAAiB,aAAa,OAAO,OAAO;gCAC3D,WAAU;gCACV,OAAO,aAAa,OAAO,YAAY;0CAEtC,MAAM,QAAQ;;;;;;0CAIjB,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM;0CAE9B,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAO3B", "debugId": null}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Sidebar from '../../components/Sidebar';\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const [userRole, setUserRole] = useState<'admin' | 'consultant' | 'project-manager' | 'trainee'>('admin');\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  // Mock: Get user data from localStorage or context\n  useEffect(() => {\n    // In a real app, this would come from authentication context or API\n    const mockUserRole = localStorage.getItem('userRole') as 'admin' | 'consultant' | 'project-manager' | 'trainee' || 'admin';\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    \n    setUserRole(mockUserRole);\n    setLanguage(mockLanguage);\n  }, []);\n\n  const handleLanguageChange = (newLanguage: 'en' | 'ar') => {\n    setLanguage(newLanguage);\n    localStorage.setItem('language', newLanguage);\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${language === 'ar' ? 'rtl' : 'ltr'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>\n      {/* Sidebar */}\n      <Sidebar \n        userRole={userRole} \n        language={language} \n        onLanguageChange={handleLanguageChange}\n      />\n      \n      {/* Main Content */}\n      <div className={`sidebar-transition ${language === 'ar' ? 'mr-72' : 'ml-72'}`}>\n        <main className=\"p-8 dashboard-content bg-gray-50 min-h-screen\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0D;IACjG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oEAAoE;QACpE,MAAM,eAAe,aAAa,OAAO,CAAC,eAAyE;QACnH,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QAExE,YAAY;QACZ,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,aAAa,OAAO,CAAC,YAAY;IACnC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,QAAQ,OAAO;QAAE,KAAK,aAAa,OAAO,QAAQ;;0BAE/G,8OAAC,6HAAA,CAAA,UAAO;gBACN,UAAU;gBACV,UAAU;gBACV,kBAAkB;;;;;;0BAIpB,8OAAC;gBAAI,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,UAAU,SAAS;0BAC3E,cAAA,8OAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}