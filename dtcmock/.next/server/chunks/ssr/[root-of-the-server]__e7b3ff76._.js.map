{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function LoginPage() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    role: ''\n  });\n\n  const roles = [\n    { value: 'admin', labelEn: 'Admin', labelAr: 'مدير' },\n    { value: 'consultant', labelEn: 'Consultant', labelAr: 'مستشار' },\n    { value: 'project-manager', labelEn: 'Project Manager', labelAr: 'مدير مشروع' },\n    { value: 'trainee', labelEn: 'Trainee', labelAr: 'متدرب' }\n  ];\n\n  const text = {\n    en: {\n      title: 'Welcome Back',\n      subtitle: 'Sign in to your account',\n      email: 'Email Address',\n      password: 'Password',\n      role: 'Select Role',\n      signIn: 'Sign In',\n      forgotPassword: 'Forgot Password?',\n      companyName: 'DTC Accelerator',\n      tagline: 'Empowering Digital Transformation'\n    },\n    ar: {\n      title: 'مرحباً بعودتك',\n      subtitle: 'تسجيل الدخول إلى حسابك',\n      email: 'عنوان البريد الإلكتروني',\n      password: 'كلمة المرور',\n      role: 'اختر الدور',\n      signIn: 'تسجيل الدخول',\n      forgotPassword: 'نسيت كلمة المرور؟',\n      companyName: 'مسرع التحول الرقمي',\n      tagline: 'تمكين التحول الرقمي'\n    }\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Mock authentication - just log the data\n    console.log('Login attempt:', formData);\n    alert(`Mock login successful for ${formData.email} as ${formData.role}`);\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen flex\" dir={language === 'ar' ? 'rtl' : 'ltr'}>\n      {/* Left Side - Animated Image Section (70%) */}\n      <div className=\"hidden lg:flex lg:w-[70%] bg-gradient-to-br from-emerald-600 via-emerald-700 to-emerald-800 relative overflow-hidden\" style={{\n        background: `linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)`\n      }}>\n        {/* Background Pattern */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"absolute top-20 left-20 w-32 h-32 bg-white rounded-full float-animation\"></div>\n          <div className=\"absolute top-40 right-32 w-24 h-24 bg-white rounded-full float-animation\" style={{animationDelay: '1s'}}></div>\n          <div className=\"absolute bottom-32 left-32 w-20 h-20 bg-white rounded-full float-animation\" style={{animationDelay: '2s'}}></div>\n          <div className=\"absolute bottom-20 right-20 w-28 h-28 bg-white rounded-full float-animation\" style={{animationDelay: '0.5s'}}></div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"flex flex-col justify-center items-center w-full p-12 text-white relative z-10\">\n          <div className=\"text-center fade-in-up\">\n            {/* Company Logo/Icon */}\n            <div className=\"mb-8 pulse-animation\">\n              <div className=\"w-24 h-24 bg-white rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-12 h-12\" style={{color: 'var(--emerald-green)'}} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\n                </svg>\n              </div>\n            </div>\n\n            <h1 className=\"text-5xl font-bold mb-4\">\n              {text[language].companyName}\n            </h1>\n            <p className=\"text-xl mb-8 opacity-90\">\n              {text[language].tagline}\n            </p>\n\n            {/* Business Graphics */}\n            <div className=\"grid grid-cols-3 gap-8 mt-12 opacity-80\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2 float-animation\">\n                  <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n                  </svg>\n                </div>\n                <p className=\"text-sm\">{language === 'ar' ? 'التحليلات' : 'Analytics'}</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2 float-animation\" style={{animationDelay: '0.5s'}}>\n                  <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                  </svg>\n                </div>\n                <p className=\"text-sm\">{language === 'ar' ? 'الجودة' : 'Quality'}</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-2 float-animation\" style={{animationDelay: '1s'}}>\n                  <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-1 16H9V7h9v14z\"/>\n                  </svg>\n                </div>\n                <p className=\"text-sm\">{language === 'ar' ? 'الابتكار' : 'Innovation'}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Right Side - Login Form (30%) */}\n      <div className=\"w-full lg:w-[30%] flex items-center justify-center p-8 bg-gray-50\">\n        <div className=\"w-full max-w-sm slide-in-right\">\n          {/* Language Toggle */}\n          <div className={`flex mb-8 ${language === 'ar' ? 'justify-start' : 'justify-end'}`}>\n            <button\n              onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}\n              className=\"px-3 py-1.5 text-sm text-white rounded-md hover:opacity-90 transition-all duration-200 font-medium\"\n              style={{backgroundColor: '#026c4a'}}\n            >\n              {language === 'en' ? 'العربية' : 'English'}\n            </button>\n          </div>\n\n          <div className={language === 'ar' ? 'arabic' : 'english'} dir={language === 'ar' ? 'rtl' : 'ltr'}>\n            <div className={`mb-8 ${language === 'ar' ? 'text-right' : 'text-center'}`}>\n              <h2 className=\"text-3xl font-bold mb-3 text-gray-900\">\n                {text[language].title}\n              </h2>\n              <p className=\"text-gray-600 text-base\">\n                {text[language].subtitle}\n              </p>\n            </div>\n\n            <form onSubmit={handleSubmit} className=\"space-y-5\">\n              <div className=\"space-y-1\">\n                <label className={`block text-sm font-semibold ${language === 'ar' ? 'text-right' : 'text-left'}`} style={{color: '#374151'}}>\n                  {text[language].email}\n                </label>\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  required\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 ${language === 'ar' ? 'text-right' : 'text-left'}`}\n                  style={{\n                    fontSize: '16px',\n                    backgroundColor: '#ffffff',\n                    color: '#111827'\n                  }}\n                  placeholder={text[language].email}\n                />\n              </div>\n\n              <div className=\"space-y-1\">\n                <label className={`block text-sm font-semibold ${language === 'ar' ? 'text-right' : 'text-left'}`} style={{color: '#374151'}}>\n                  {text[language].password}\n                </label>\n                <input\n                  type=\"password\"\n                  name=\"password\"\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  required\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 ${language === 'ar' ? 'text-right' : 'text-left'}`}\n                  style={{\n                    fontSize: '16px',\n                    backgroundColor: '#ffffff',\n                    color: '#111827'\n                  }}\n                  placeholder={text[language].password}\n                />\n              </div>\n\n              <div className=\"space-y-1\">\n                <label className={`block text-sm font-semibold ${language === 'ar' ? 'text-right' : 'text-left'}`} style={{color: '#374151'}}>\n                  {text[language].role}\n                </label>\n                <select\n                  name=\"role\"\n                  value={formData.role}\n                  onChange={handleInputChange}\n                  required\n                  className={`w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 ${language === 'ar' ? 'text-right' : 'text-left'}`}\n                  style={{\n                    fontSize: '16px',\n                    backgroundColor: '#ffffff',\n                    color: '#111827'\n                  }}\n                >\n                  <option value=\"\" style={{color: '#9CA3AF'}}>{text[language].role}</option>\n                  {roles.map((role) => (\n                    <option key={role.value} value={role.value} style={{color: '#111827'}}>\n                      {language === 'en' ? role.labelEn : role.labelAr}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div className=\"pt-2\">\n                <button\n                  type=\"submit\"\n                  className=\"w-full text-white py-3 px-4 rounded-md font-semibold text-base transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transform hover:scale-[1.02]\"\n                  style={{\n                    backgroundColor: '#026c4a',\n                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'\n                  }}\n                >\n                  {text[language].signIn}\n                </button>\n              </div>\n\n              <div className={`pt-2 ${language === 'ar' ? 'text-right' : 'text-center'}`}>\n                <a\n                  href=\"#\"\n                  className=\"text-sm font-medium transition-colors duration-200 hover:underline\"\n                  style={{color: '#026c4a'}}\n                >\n                  {text[language].forgotPassword}\n                </a>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAS,SAAS;YAAS,SAAS;QAAO;QACpD;YAAE,OAAO;YAAc,SAAS;YAAc,SAAS;QAAS;QAChE;YAAE,OAAO;YAAmB,SAAS;YAAmB,SAAS;QAAa;QAC9E;YAAE,OAAO;YAAW,SAAS;YAAW,SAAS;QAAQ;KAC1D;IAED,MAAM,OAAO;QACX,IAAI;YACF,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,gBAAgB;YAChB,aAAa;YACb,SAAS;QACX;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,gBAAgB;YAChB,aAAa;YACb,SAAS;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,0CAA0C;QAC1C,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,MAAM,CAAC,0BAA0B,EAAE,SAAS,KAAK,CAAC,IAAI,EAAE,SAAS,IAAI,EAAE;IACzE;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAoB,KAAK,aAAa,OAAO,QAAQ;;0BAElE,8OAAC;gBAAI,WAAU;gBAAuH,OAAO;oBAC3I,YAAY,CAAC,0EAA0E,CAAC;gBAC1F;;kCAEE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;gCAA2E,OAAO;oCAAC,gBAAgB;gCAAI;;;;;;0CACtH,8OAAC;gCAAI,WAAU;gCAA6E,OAAO;oCAAC,gBAAgB;gCAAI;;;;;;0CACxH,8OAAC;gCAAI,WAAU;gCAA8E,OAAO;oCAAC,gBAAgB;gCAAM;;;;;;;;;;;;kCAI7H,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAY,OAAO;gDAAC,OAAO;4CAAsB;4CAAG,MAAK;4CAAe,SAAQ;sDAC7F,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;8CAKd,8OAAC;oCAAG,WAAU;8CACX,IAAI,CAAC,SAAS,CAAC,WAAW;;;;;;8CAE7B,8OAAC;oCAAE,WAAU;8CACV,IAAI,CAAC,SAAS,CAAC,OAAO;;;;;;8CAIzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,WAAU;8DAAW,aAAa,OAAO,cAAc;;;;;;;;;;;;sDAE5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAA4G,OAAO;wDAAC,gBAAgB;oDAAM;8DACvJ,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,WAAU;8DAAW,aAAa,OAAO,WAAW;;;;;;;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAA4G,OAAO;wDAAC,gBAAgB;oDAAI;8DACrJ,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,WAAU;8DAAW,aAAa,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAW,CAAC,UAAU,EAAE,aAAa,OAAO,kBAAkB,eAAe;sCAChF,cAAA,8OAAC;gCACC,SAAS,IAAM,YAAY,aAAa,OAAO,OAAO;gCACtD,WAAU;gCACV,OAAO;oCAAC,iBAAiB;gCAAS;0CAEjC,aAAa,OAAO,YAAY;;;;;;;;;;;sCAIrC,8OAAC;4BAAI,WAAW,aAAa,OAAO,WAAW;4BAAW,KAAK,aAAa,OAAO,QAAQ;;8CACzF,8OAAC;oCAAI,WAAW,CAAC,KAAK,EAAE,aAAa,OAAO,eAAe,eAAe;;sDACxE,8OAAC;4CAAG,WAAU;sDACX,IAAI,CAAC,SAAS,CAAC,KAAK;;;;;;sDAEvB,8OAAC;4CAAE,WAAU;sDACV,IAAI,CAAC,SAAS,CAAC,QAAQ;;;;;;;;;;;;8CAI5B,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAW,CAAC,4BAA4B,EAAE,aAAa,OAAO,eAAe,aAAa;oDAAE,OAAO;wDAAC,OAAO;oDAAS;8DACxH,IAAI,CAAC,SAAS,CAAC,KAAK;;;;;;8DAEvB,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,QAAQ;oDACR,WAAW,CAAC,yKAAyK,EAAE,aAAa,OAAO,eAAe,aAAa;oDACvO,OAAO;wDACL,UAAU;wDACV,iBAAiB;wDACjB,OAAO;oDACT;oDACA,aAAa,IAAI,CAAC,SAAS,CAAC,KAAK;;;;;;;;;;;;sDAIrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAW,CAAC,4BAA4B,EAAE,aAAa,OAAO,eAAe,aAAa;oDAAE,OAAO;wDAAC,OAAO;oDAAS;8DACxH,IAAI,CAAC,SAAS,CAAC,QAAQ;;;;;;8DAE1B,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,QAAQ;oDACR,WAAW,CAAC,yKAAyK,EAAE,aAAa,OAAO,eAAe,aAAa;oDACvO,OAAO;wDACL,UAAU;wDACV,iBAAiB;wDACjB,OAAO;oDACT;oDACA,aAAa,IAAI,CAAC,SAAS,CAAC,QAAQ;;;;;;;;;;;;sDAIxC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAW,CAAC,4BAA4B,EAAE,aAAa,OAAO,eAAe,aAAa;oDAAE,OAAO;wDAAC,OAAO;oDAAS;8DACxH,IAAI,CAAC,SAAS,CAAC,IAAI;;;;;;8DAEtB,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,QAAQ;oDACR,WAAW,CAAC,yKAAyK,EAAE,aAAa,OAAO,eAAe,aAAa;oDACvO,OAAO;wDACL,UAAU;wDACV,iBAAiB;wDACjB,OAAO;oDACT;;sEAEA,8OAAC;4DAAO,OAAM;4DAAG,OAAO;gEAAC,OAAO;4DAAS;sEAAI,IAAI,CAAC,SAAS,CAAC,IAAI;;;;;;wDAC/D,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gEAAwB,OAAO,KAAK,KAAK;gEAAE,OAAO;oEAAC,OAAO;gEAAS;0EACjE,aAAa,OAAO,KAAK,OAAO,GAAG,KAAK,OAAO;+DADrC,KAAK,KAAK;;;;;;;;;;;;;;;;;sDAO7B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,OAAO;oDACL,iBAAiB;oDACjB,WAAW;gDACb;0DAEC,IAAI,CAAC,SAAS,CAAC,MAAM;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAW,CAAC,KAAK,EAAE,aAAa,OAAO,eAAe,eAAe;sDACxE,cAAA,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAC,OAAO;gDAAS;0DAEvB,IAAI,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD", "debugId": null}}, {"offset": {"line": 616, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 644, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}