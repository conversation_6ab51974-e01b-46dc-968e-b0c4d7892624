{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport default function DashboardHome() {\n  const [userRole, setUserRole] = useState<'admin' | 'consultant' | 'project-manager' | 'trainee'>('admin');\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockUserRole = localStorage.getItem('userRole') as 'admin' | 'consultant' | 'project-manager' | 'trainee' || 'admin';\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    \n    setUserRole(mockUserRole);\n    setLanguage(mockLanguage);\n  }, []);\n\n  const content = {\n    en: {\n      welcome: 'Welcome to DTC Accelerator',\n      adminDashboard: 'Admin Dashboard',\n      consultantDashboard: 'Consultant Dashboard',\n      projectManagerDashboard: 'Project Manager Dashboard',\n      traineeDashboard: 'Trainee Dashboard',\n      overview: 'Dashboard Overview',\n      description: 'This is your personalized dashboard based on your role and permissions.',\n      quickStats: 'Quick Statistics',\n      recentActivity: 'Recent Activity'\n    },\n    ar: {\n      welcome: 'مرحباً بك في مسرع التحول الرقمي',\n      adminDashboard: 'لوحة تحكم المدير',\n      consultantDashboard: 'لوحة تحكم المستشار',\n      projectManagerDashboard: 'لوحة تحكم مدير المشروع',\n      traineeDashboard: 'لوحة تحكم المتدرب',\n      overview: 'نظرة عامة على لوحة التحكم',\n      description: 'هذه لوحة التحكم الشخصية الخاصة بك بناءً على دورك وصلاحياتك.',\n      quickStats: 'إحصائيات سريعة',\n      recentActivity: 'النشاط الأخير'\n    }\n  };\n\n  const getDashboardTitle = () => {\n    switch (userRole) {\n      case 'admin':\n        return content[language].adminDashboard;\n      case 'consultant':\n        return content[language].consultantDashboard;\n      case 'project-manager':\n        return content[language].projectManagerDashboard;\n      case 'trainee':\n        return content[language].traineeDashboard;\n      default:\n        return content[language].adminDashboard;\n    }\n  };\n\n  const getStatsForRole = () => {\n    switch (userRole) {\n      case 'admin':\n        return [\n          { label: language === 'en' ? 'Total Users' : 'إجمالي المستخدمين', value: '1,234', icon: '👥' },\n          { label: language === 'en' ? 'Active Projects' : 'المشاريع النشطة', value: '56', icon: '📊' },\n          { label: language === 'en' ? 'Frameworks' : 'الأطر', value: '12', icon: '🏗️' },\n          { label: language === 'en' ? 'Training Courses' : 'الدورات التدريبية', value: '89', icon: '🎓' }\n        ];\n      case 'consultant':\n        return [\n          { label: language === 'en' ? 'My Projects' : 'مشاريعي', value: '8', icon: '📊' },\n          { label: language === 'en' ? 'Consultations' : 'الاستشارات', value: '24', icon: '💼' },\n          { label: language === 'en' ? 'Completed Tasks' : 'المهام المكتملة', value: '156', icon: '✅' }\n        ];\n      case 'project-manager':\n        return [\n          { label: language === 'en' ? 'Managed Projects' : 'المشاريع المدارة', value: '12', icon: '📊' },\n          { label: language === 'en' ? 'Team Members' : 'أعضاء الفريق', value: '45', icon: '👥' },\n          { label: language === 'en' ? 'Milestones' : 'المعالم', value: '78', icon: '🎯' }\n        ];\n      case 'trainee':\n        return [\n          { label: language === 'en' ? 'Enrolled Courses' : 'الدورات المسجلة', value: '6', icon: '🎓' },\n          { label: language === 'en' ? 'Completed Modules' : 'الوحدات المكتملة', value: '23', icon: '✅' },\n          { label: language === 'en' ? 'Certificates' : 'الشهادات', value: '3', icon: '🏆' }\n        ];\n      default:\n        return [];\n    }\n  };\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className={`text-3xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].welcome}\n        </h1>\n        <h2 className={`text-xl ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--emerald-green)' }}>\n          {getDashboardTitle()}\n        </h2>\n      </div>\n\n      {/* Overview Card */}\n      <div className=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n        <h3 className={`text-lg font-semibold mb-3 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].overview}\n        </h3>\n        <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n          {content[language].description}\n        </p>\n      </div>\n\n      {/* Quick Statistics */}\n      <div className=\"mb-8\">\n        <h3 className={`text-lg font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].quickStats}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {getStatsForRole().map((stat, index) => (\n            <div key={index} className=\"bg-white rounded-lg shadow-md p-6\">\n              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                <div \n                  className=\"w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl\"\n                  style={{ backgroundColor: 'var(--emerald-green)' }}\n                >\n                  {stat.icon}\n                </div>\n                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                  <p className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {stat.value}\n                  </p>\n                  <p className={`text-sm text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {stat.label}\n                  </p>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <h3 className={`text-lg font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].recentActivity}\n        </h3>\n        <div className=\"space-y-3\">\n          {[1, 2, 3].map((item) => (\n            <div key={item} className={`flex items-center p-3 bg-gray-50 rounded-lg ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <div \n                className=\"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm\"\n                style={{ backgroundColor: 'var(--emerald-green)' }}\n              >\n                📝\n              </div>\n              <div className={`${language === 'ar' ? 'mr-3 text-right' : 'ml-3 text-left'}`}>\n                <p className={`text-sm font-medium ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                  {language === 'en' ? `Recent activity item ${item}` : `عنصر النشاط الأخير ${item}`}\n                </p>\n                <p className={`text-xs text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                  {language === 'en' ? '2 hours ago' : 'منذ ساعتين'}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0D;IACjG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAAyE;QACnH,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QAExE,YAAY;QACZ,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,SAAS;YACT,gBAAgB;YAChB,qBAAqB;YACrB,yBAAyB;YACzB,kBAAkB;YAClB,UAAU;YACV,aAAa;YACb,YAAY;YACZ,gBAAgB;QAClB;QACA,IAAI;YACF,SAAS;YACT,gBAAgB;YAChB,qBAAqB;YACrB,yBAAyB;YACzB,kBAAkB;YAClB,UAAU;YACV,aAAa;YACb,YAAY;YACZ,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,cAAc;YACzC,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,mBAAmB;YAC9C,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,uBAAuB;YAClD,KAAK;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,gBAAgB;YAC3C;gBACE,OAAO,OAAO,CAAC,SAAS,CAAC,cAAc;QAC3C;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,gBAAgB;wBAAqB,OAAO;wBAAS,MAAM;oBAAK;oBAC7F;wBAAE,OAAO,aAAa,OAAO,oBAAoB;wBAAmB,OAAO;wBAAM,MAAM;oBAAK;oBAC5F;wBAAE,OAAO,aAAa,OAAO,eAAe;wBAAS,OAAO;wBAAM,MAAM;oBAAM;oBAC9E;wBAAE,OAAO,aAAa,OAAO,qBAAqB;wBAAqB,OAAO;wBAAM,MAAM;oBAAK;iBAChG;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,gBAAgB;wBAAW,OAAO;wBAAK,MAAM;oBAAK;oBAC/E;wBAAE,OAAO,aAAa,OAAO,kBAAkB;wBAAc,OAAO;wBAAM,MAAM;oBAAK;oBACrF;wBAAE,OAAO,aAAa,OAAO,oBAAoB;wBAAmB,OAAO;wBAAO,MAAM;oBAAI;iBAC7F;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,qBAAqB;wBAAoB,OAAO;wBAAM,MAAM;oBAAK;oBAC9F;wBAAE,OAAO,aAAa,OAAO,iBAAiB;wBAAgB,OAAO;wBAAM,MAAM;oBAAK;oBACtF;wBAAE,OAAO,aAAa,OAAO,eAAe;wBAAW,OAAO;wBAAM,MAAM;oBAAK;iBAChF;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,OAAO,aAAa,OAAO,qBAAqB;wBAAmB,OAAO;wBAAK,MAAM;oBAAK;oBAC5F;wBAAE,OAAO,aAAa,OAAO,sBAAsB;wBAAoB,OAAO;wBAAM,MAAM;oBAAI;oBAC9F;wBAAE,OAAO,aAAa,OAAO,iBAAiB;wBAAY,OAAO;wBAAK,MAAM;oBAAK;iBAClF;YACH;gBACE,OAAO,EAAE;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,aAAa,OAAO,eAAe,aAAa;;0BAEjE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;wBAAE,OAAO;4BAAE,OAAO;wBAAuB;kCACxH,OAAO,CAAC,SAAS,CAAC,OAAO;;;;;;kCAE5B,8OAAC;wBAAG,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,gBAAgB,IAAI;wBAAE,OAAO;4BAAE,OAAO;wBAAuB;kCACxG;;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAW,CAAC,2BAA2B,EAAE,aAAa,OAAO,gBAAgB,IAAI;wBAAE,OAAO;4BAAE,OAAO;wBAAuB;kCAC3H,OAAO,CAAC,SAAS,CAAC,QAAQ;;;;;;kCAE7B,8OAAC;wBAAE,WAAW,CAAC,cAAc,EAAE,aAAa,OAAO,gBAAgB,IAAI;kCACpE,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;;;;;;;0BAKlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAW,CAAC,2BAA2B,EAAE,aAAa,OAAO,gBAAgB,IAAI;wBAAE,OAAO;4BAAE,OAAO;wBAAuB;kCAC3H,OAAO,CAAC,SAAS,CAAC,UAAU;;;;;;kCAE/B,8OAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,MAAM,sBAC5B,8OAAC;gCAAgB,WAAU;0CACzB,cAAA,8OAAC;oCAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;sDACxF,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB;4CAAuB;sDAEhD,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAI,WAAW,GAAG,aAAa,OAAO,oBAAoB,kBAAkB;;8DAC3E,8OAAC;oDAAE,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,gBAAgB,IAAI;oDAAE,OAAO;wDAAE,OAAO;oDAAuB;8DAClH,KAAK,KAAK;;;;;;8DAEb,8OAAC;oDAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;8DAC5E,KAAK,KAAK;;;;;;;;;;;;;;;;;;+BAbT;;;;;;;;;;;;;;;;0BAuBhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAW,CAAC,2BAA2B,EAAE,aAAa,OAAO,gBAAgB,IAAI;wBAAE,OAAO;4BAAE,OAAO;wBAAuB;kCAC3H,OAAO,CAAC,SAAS,CAAC,cAAc;;;;;;kCAEnC,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,qBACd,8OAAC;gCAAe,WAAW,CAAC,4CAA4C,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kDAC7H,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAuB;kDAClD;;;;;;kDAGD,8OAAC;wCAAI,WAAW,GAAG,aAAa,OAAO,oBAAoB,kBAAkB;;0DAC3E,8OAAC;gDAAE,WAAW,CAAC,oBAAoB,EAAE,aAAa,OAAO,gBAAgB,IAAI;gDAAE,OAAO;oDAAE,OAAO;gDAAuB;0DACnH,aAAa,OAAO,CAAC,qBAAqB,EAAE,MAAM,GAAG,CAAC,mBAAmB,EAAE,MAAM;;;;;;0DAEpF,8OAAC;gDAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;0DAC5E,aAAa,OAAO,gBAAgB;;;;;;;;;;;;;+BAZjC;;;;;;;;;;;;;;;;;;;;;;AAqBtB", "debugId": null}}]}