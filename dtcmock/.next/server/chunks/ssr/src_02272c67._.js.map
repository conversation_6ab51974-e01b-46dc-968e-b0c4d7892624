{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div\n      className={`relative overflow-hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-12 py-16\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYe,SAAS,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa;IACzF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QACxE,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,WAAW,CAAC,yBAAyB,EAAE,aAAa,OAAO,eAAe,aAAa;QACvF,OAAO;YACL,YAAY;QACd;;0BAIA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,iDAAiD,EAAE,aAAa,OAAO,YAAY,UAAU;;;;;;kCAC9G,8OAAC;wBAAI,WAAW,CAAC,6DAA6D,EAAE,aAAa,OAAO,YAAY,YAAY;;;;;;kCAC5H,8OAAC;wBAAI,WAAW,CAAC,mDAAmD,EAAE,aAAa,OAAO,aAAa,aAAa;;;;;;;;;;;;0BAItH,8OAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAW,CAAC,kDAAkD,EAAE,aAAa,OAAO,qCAAqC,IAAI;sCAC9H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,8OAAC;4CACC,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,oBAAoB,QAAQ;4CACtE,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,8OAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,8OAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,8OAAC;wBAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;4BAEvF,sBACC,8OAAC;gCAAI,WAAW,CAAC,cAAc,EAAE,aAAa,OAAO,SAAS,QAAQ;0CACpE,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;;oCAEZ,0BACC,8OAAC;wCAAE,WAAW,CAAC,gEAAgE,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACtH;;;;;;kDAKL,8OAAC;wCAAG,WAAW,CAAC,6DAA6D,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACpH;;;;;;oCAIF,6BACC,8OAAC;wCAAE,WAAW,CAAC,gDAAgD,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACtG;;;;;;;;;;;;;;;;;;kCAOT,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/frameworks/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Hero from '../../../components/Hero';\n\nexport default function FrameworkManagement() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  const content = {\n    en: {\n      title: 'Framework Management',\n      subtitle: 'Digital Transformation',\n      description: 'Manage digital transformation frameworks, methodologies, and best practices to drive organizational change.',\n      placeholder: 'Framework management functionality will be implemented here.'\n    },\n    ar: {\n      title: 'إدارة الإطار',\n      subtitle: 'التحول الرقمي',\n      description: 'إدارة أطر ومنهجيات وأفضل الممارسات للتحول الرقمي لدفع التغيير التنظيمي.',\n      placeholder: 'سيتم تنفيذ وظائف إدارة الإطار هنا.'\n    }\n  };\n\n  const frameworkIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n    </svg>\n  );\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      <Hero\n        title={content[language].title}\n        subtitle={content[language].subtitle}\n        description={content[language].description}\n        icon={frameworkIcon}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },\n          { label: content[language].title }\n        ]}\n      />\n\n      <div className=\"bg-white\">\n        <div className=\"px-12 py-12\">\n          {/* Framework Categories */}\n          <div className={`flex gap-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n\n            {/* Data Management Frameworks Card */}\n            <div className=\"flex-1 group\">\n              <div className=\"relative overflow-hidden rounded-3xl bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 border border-blue-200 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2\">\n                {/* Background Pattern */}\n                <div className=\"absolute inset-0 opacity-10\">\n                  <div className=\"absolute top-4 right-4 w-32 h-32 rounded-full bg-blue-300\"></div>\n                  <div className=\"absolute bottom-4 left-4 w-24 h-24 rounded-lg bg-blue-400 rotate-45\"></div>\n                  <div className=\"absolute top-1/2 left-1/3 w-16 h-16 rounded-full bg-blue-200\"></div>\n                </div>\n\n                <div className=\"relative p-8\">\n                  {/* Header */}\n                  <div className={`flex items-center mb-6 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                    <div className=\"w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4\" />\n                      </svg>\n                    </div>\n                    <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                      <h3 className={`text-2xl font-bold text-blue-800 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {language === 'en' ? 'Data Management' : 'إدارة البيانات'}\n                      </h3>\n                      <p className={`text-blue-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {language === 'en' ? 'Frameworks' : 'الأطر'}\n                      </p>\n                    </div>\n                  </div>\n\n                  {/* Description */}\n                  <p className={`text-blue-700 mb-6 leading-relaxed ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`}>\n                    {language === 'en'\n                      ? 'Comprehensive frameworks for data governance, quality management, and analytics to drive data-driven decision making across your organization.'\n                      : 'أطر شاملة لحوكمة البيانات وإدارة الجودة والتحليلات لدفع اتخاذ القرارات المبنية على البيانات عبر مؤسستك.'\n                    }\n                  </p>\n\n                  {/* Features */}\n                  <div className=\"space-y-3 mb-8\">\n                    {[\n                      { en: 'Data Governance Frameworks', ar: 'أطر حوكمة البيانات' },\n                      { en: 'Data Quality Management', ar: 'إدارة جودة البيانات' },\n                      { en: 'Analytics & BI Frameworks', ar: 'أطر التحليلات وذكاء الأعمال' },\n                      { en: 'Data Security & Privacy', ar: 'أمان البيانات والخصوصية' }\n                    ].map((feature, index) => (\n                      <div key={index} className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                        <div className=\"w-2 h-2 rounded-full bg-blue-500\"></div>\n                        <span className={`text-blue-700 font-medium ${language === 'ar' ? 'mr-3 font-arabic' : 'ml-3'}`}>\n                          {feature[language]}\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Action Button */}\n                  <button className={`w-full py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {language === 'en' ? 'Explore Frameworks' : 'استكشاف الأطر'}\n                  </button>\n\n                  {/* Stats */}\n                  <div className={`flex justify-between mt-6 pt-6 border-t border-blue-300 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                    <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                      <div className={`text-2xl font-bold text-blue-800 ${language === 'ar' ? 'font-arabic' : ''}`}>12</div>\n                      <div className={`text-xs text-blue-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {language === 'en' ? 'Frameworks' : 'إطار'}\n                      </div>\n                    </div>\n                    <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                      <div className={`text-2xl font-bold text-blue-800 ${language === 'ar' ? 'font-arabic' : ''}`}>8</div>\n                      <div className={`text-xs text-blue-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {language === 'en' ? 'Active' : 'نشط'}\n                      </div>\n                    </div>\n                    <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                      <div className={`text-2xl font-bold text-blue-800 ${language === 'ar' ? 'font-arabic' : ''}`}>95%</div>\n                      <div className={`text-xs text-blue-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {language === 'en' ? 'Compliance' : 'الامتثال'}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Enterprise Architecture Frameworks Card */}\n            <div className=\"flex-1 group\">\n              <div className=\"relative overflow-hidden rounded-3xl bg-gradient-to-br from-purple-50 via-purple-100 to-purple-200 border border-purple-200 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2\">\n                {/* Background Pattern */}\n                <div className=\"absolute inset-0 opacity-10\">\n                  <div className=\"absolute top-4 left-4 w-32 h-32 rounded-full bg-purple-300\"></div>\n                  <div className=\"absolute bottom-4 right-4 w-24 h-24 rounded-lg bg-purple-400 rotate-45\"></div>\n                  <div className=\"absolute top-1/2 right-1/3 w-16 h-16 rounded-full bg-purple-200\"></div>\n                </div>\n\n                <div className=\"relative p-8\">\n                  {/* Header */}\n                  <div className={`flex items-center mb-6 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                    <div className=\"w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                      </svg>\n                    </div>\n                    <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>\n                      <h3 className={`text-2xl font-bold text-purple-800 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {language === 'en' ? 'Enterprise Architecture' : 'هندسة المؤسسة'}\n                      </h3>\n                      <p className={`text-purple-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {language === 'en' ? 'Frameworks' : 'الأطر'}\n                      </p>\n                    </div>\n                  </div>\n\n                  {/* Description */}\n                  <p className={`text-purple-700 mb-6 leading-relaxed ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`}>\n                    {language === 'en'\n                      ? 'Strategic frameworks for enterprise architecture design, implementation, and governance to align technology with business objectives and drive digital transformation.'\n                      : 'أطر استراتيجية لتصميم وتنفيذ وحوكمة هندسة المؤسسة لمواءمة التكنولوجيا مع أهداف الأعمال ودفع التحول الرقمي.'\n                    }\n                  </p>\n\n                  {/* Features */}\n                  <div className=\"space-y-3 mb-8\">\n                    {[\n                      { en: 'TOGAF & ArchiMate', ar: 'TOGAF و ArchiMate' },\n                      { en: 'Business Architecture', ar: 'هندسة الأعمال' },\n                      { en: 'Technology Architecture', ar: 'هندسة التكنولوجيا' },\n                      { en: 'Solution Architecture', ar: 'هندسة الحلول' }\n                    ].map((feature, index) => (\n                      <div key={index} className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                        <div className=\"w-2 h-2 rounded-full bg-purple-500\"></div>\n                        <span className={`text-purple-700 font-medium ${language === 'ar' ? 'mr-3 font-arabic' : 'ml-3'}`}>\n                          {feature[language]}\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Action Button */}\n                  <button className={`w-full py-4 bg-gradient-to-r from-purple-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                    {language === 'en' ? 'Explore Frameworks' : 'استكشاف الأطر'}\n                  </button>\n\n                  {/* Stats */}\n                  <div className={`flex justify-between mt-6 pt-6 border-t border-purple-300 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n                    <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                      <div className={`text-2xl font-bold text-purple-800 ${language === 'ar' ? 'font-arabic' : ''}`}>15</div>\n                      <div className={`text-xs text-purple-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {language === 'en' ? 'Frameworks' : 'إطار'}\n                      </div>\n                    </div>\n                    <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                      <div className={`text-2xl font-bold text-purple-800 ${language === 'ar' ? 'font-arabic' : ''}`}>11</div>\n                      <div className={`text-xs text-purple-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {language === 'en' ? 'Active' : 'نشط'}\n                      </div>\n                    </div>\n                    <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n                      <div className={`text-2xl font-bold text-purple-800 ${language === 'ar' ? 'font-arabic' : ''}`}>88%</div>\n                      <div className={`text-xs text-purple-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>\n                        {language === 'en' ? 'Maturity' : 'النضج'}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Additional Information Section */}\n          <div className=\"mt-12 p-8 bg-gradient-to-r from-gray-50 to-gray-100 rounded-3xl border border-gray-200\">\n            <div className={`text-center mb-8 ${language === 'ar' ? 'text-right' : 'text-left'}`}>\n              <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {language === 'en' ? 'Framework Implementation Journey' : 'رحلة تنفيذ الأطر'}\n              </h3>\n              <p className={`text-gray-600 max-w-4xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {language === 'en'\n                  ? 'Our comprehensive framework management approach ensures successful digital transformation through structured methodologies, best practices, and continuous improvement processes.'\n                  : 'نهجنا الشامل لإدارة الأطر يضمن نجاح التحول الرقمي من خلال المنهجيات المنظمة وأفضل الممارسات وعمليات التحسين المستمر.'\n                }\n              </p>\n            </div>\n\n            {/* Process Steps */}\n            <div className={`flex justify-between items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              {[\n                { en: 'Assessment', ar: 'التقييم', icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2' },\n                { en: 'Design', ar: 'التصميم', icon: 'M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z' },\n                { en: 'Implementation', ar: 'التنفيذ', icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z' },\n                { en: 'Optimization', ar: 'التحسين', icon: 'M13 10V3L4 14h7v7l9-11h-7z' }\n              ].map((step, index) => (\n                <div key={index} className={`flex flex-col items-center ${language === 'ar' ? 'text-right' : 'text-center'}`}>\n                  <div\n                    className=\"w-16 h-16 rounded-2xl flex items-center justify-center text-white mb-4 shadow-lg\"\n                    style={{ backgroundColor: 'var(--emerald-green)' }}\n                  >\n                    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={step.icon} />\n                    </svg>\n                  </div>\n                  <h4 className={`font-bold text-lg ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n                    {step[language]}\n                  </h4>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QACxE,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;IACF;IAEA,MAAM,8BACJ,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,qBACE,8OAAC;QAAI,WAAW,GAAG,aAAa,OAAO,eAAe,aAAa;;0BACjE,8OAAC,0HAAA,CAAA,UAAI;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;gBAC9B,UAAU,OAAO,CAAC,SAAS,CAAC,QAAQ;gBACpC,aAAa,OAAO,CAAC,SAAS,CAAC,WAAW;gBAC1C,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;wBAAe,MAAM;oBAAa;oBAC7E;wBAAE,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;oBAAC;iBAClC;;;;;;0BAGH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAW,CAAC,WAAW,EAAE,aAAa,OAAO,qBAAqB,YAAY;;8CAGjF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAW,CAAC,uBAAuB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;0EAC7F,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;oEAAU,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACjE,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAI,WAAW,GAAG,aAAa,OAAO,oBAAoB,kBAAkB;;kFAC3E,8OAAC;wEAAG,WAAW,CAAC,iCAAiC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFACxF,aAAa,OAAO,oBAAoB;;;;;;kFAE3C,8OAAC;wEAAE,WAAW,CAAC,0BAA0B,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAChF,aAAa,OAAO,eAAe;;;;;;;;;;;;;;;;;;kEAM1C,8OAAC;wDAAE,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,2BAA2B,aAAa;kEAC7G,aAAa,OACV,mJACA;;;;;;kEAKN,8OAAC;wDAAI,WAAU;kEACZ;4DACC;gEAAE,IAAI;gEAA8B,IAAI;4DAAqB;4DAC7D;gEAAE,IAAI;gEAA2B,IAAI;4DAAsB;4DAC3D;gEAAE,IAAI;gEAA6B,IAAI;4DAA8B;4DACrE;gEAAE,IAAI;gEAA2B,IAAI;4DAA0B;yDAChE,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;gEAAgB,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kFACpG,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAK,WAAW,CAAC,0BAA0B,EAAE,aAAa,OAAO,qBAAqB,QAAQ;kFAC5F,OAAO,CAAC,SAAS;;;;;;;+DAHZ;;;;;;;;;;kEAUd,8OAAC;wDAAO,WAAW,CAAC,wLAAwL,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEACnP,aAAa,OAAO,uBAAuB;;;;;;kEAI9C,8OAAC;wDAAI,WAAW,CAAC,wDAAwD,EAAE,aAAa,OAAO,qBAAqB,YAAY;;0EAC9H,8OAAC;gEAAI,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,eAAe,aAAa;;kFAC7E,8OAAC;wEAAI,WAAW,CAAC,iCAAiC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAAE;;;;;;kFAC9F,8OAAC;wEAAI,WAAW,CAAC,kCAAkC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAC1F,aAAa,OAAO,eAAe;;;;;;;;;;;;0EAGxC,8OAAC;gEAAI,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,eAAe,aAAa;;kFAC7E,8OAAC;wEAAI,WAAW,CAAC,iCAAiC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAAE;;;;;;kFAC9F,8OAAC;wEAAI,WAAW,CAAC,kCAAkC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAC1F,aAAa,OAAO,WAAW;;;;;;;;;;;;0EAGpC,8OAAC;gEAAI,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,eAAe,aAAa;;kFAC7E,8OAAC;wEAAI,WAAW,CAAC,iCAAiC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAAE;;;;;;kFAC9F,8OAAC;wEAAI,WAAW,CAAC,kCAAkC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAC1F,aAAa,OAAO,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAShD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAW,CAAC,uBAAuB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;0EAC7F,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;oEAAU,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACjE,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAI,WAAW,GAAG,aAAa,OAAO,oBAAoB,kBAAkB;;kFAC3E,8OAAC;wEAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAC1F,aAAa,OAAO,4BAA4B;;;;;;kFAEnD,8OAAC;wEAAE,WAAW,CAAC,4BAA4B,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAClF,aAAa,OAAO,eAAe;;;;;;;;;;;;;;;;;;kEAM1C,8OAAC;wDAAE,WAAW,CAAC,qCAAqC,EAAE,aAAa,OAAO,2BAA2B,aAAa;kEAC/G,aAAa,OACV,2KACA;;;;;;kEAKN,8OAAC;wDAAI,WAAU;kEACZ;4DACC;gEAAE,IAAI;gEAAqB,IAAI;4DAAoB;4DACnD;gEAAE,IAAI;gEAAyB,IAAI;4DAAgB;4DACnD;gEAAE,IAAI;gEAA2B,IAAI;4DAAoB;4DACzD;gEAAE,IAAI;gEAAyB,IAAI;4DAAe;yDACnD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;gEAAgB,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kFACpG,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAK,WAAW,CAAC,4BAA4B,EAAE,aAAa,OAAO,qBAAqB,QAAQ;kFAC9F,OAAO,CAAC,SAAS;;;;;;;+DAHZ;;;;;;;;;;kEAUd,8OAAC;wDAAO,WAAW,CAAC,gMAAgM,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAC3P,aAAa,OAAO,uBAAuB;;;;;;kEAI9C,8OAAC;wDAAI,WAAW,CAAC,0DAA0D,EAAE,aAAa,OAAO,qBAAqB,YAAY;;0EAChI,8OAAC;gEAAI,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,eAAe,aAAa;;kFAC7E,8OAAC;wEAAI,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAAE;;;;;;kFAChG,8OAAC;wEAAI,WAAW,CAAC,oCAAoC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAC5F,aAAa,OAAO,eAAe;;;;;;;;;;;;0EAGxC,8OAAC;gEAAI,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,eAAe,aAAa;;kFAC7E,8OAAC;wEAAI,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAAE;;;;;;kFAChG,8OAAC;wEAAI,WAAW,CAAC,oCAAoC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAC5F,aAAa,OAAO,WAAW;;;;;;;;;;;;0EAGpC,8OAAC;gEAAI,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,eAAe,aAAa;;kFAC7E,8OAAC;wEAAI,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAAE;;;;;;kFAChG,8OAAC;wEAAI,WAAW,CAAC,oCAAoC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kFAC5F,aAAa,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAC,iBAAiB,EAAE,aAAa,OAAO,eAAe,aAAa;;sDAClF,8OAAC;4CAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAuB;sDACxH,aAAa,OAAO,qCAAqC;;;;;;sDAE5D,8OAAC;4CAAE,WAAW,CAAC,gCAAgC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sDACtF,aAAa,OACV,sLACA;;;;;;;;;;;;8CAMR,8OAAC;oCAAI,WAAW,CAAC,kCAAkC,EAAE,aAAa,OAAO,qBAAqB,YAAY;8CACvG;wCACC;4CAAE,IAAI;4CAAc,IAAI;4CAAW,MAAM;wCAAkI;wCAC3K;4CAAE,IAAI;4CAAU,IAAI;4CAAW,MAAM;wCAAoI;wCACzK;4CAAE,IAAI;4CAAkB,IAAI;4CAAW,MAAM;wCAAse;wCACnhB;4CAAE,IAAI;4CAAgB,IAAI;4CAAW,MAAM;wCAA6B;qCACzE,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;4CAAgB,WAAW,CAAC,2BAA2B,EAAE,aAAa,OAAO,eAAe,eAAe;;8DAC1G,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAAuB;8DAEjD,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAG,KAAK,IAAI;;;;;;;;;;;;;;;;8DAGnF,8OAAC;oDAAG,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,gBAAgB,IAAI;oDAAE,OAAO;wDAAE,OAAO;oDAAuB;8DAClH,IAAI,CAAC,SAAS;;;;;;;2CAVT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoB1B", "debugId": null}}]}