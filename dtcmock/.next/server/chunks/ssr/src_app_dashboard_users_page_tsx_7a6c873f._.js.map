{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/users/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport default function UserManagement() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  const content = {\n    en: {\n      title: 'User Management',\n      description: 'Manage users, roles, and permissions across the platform.',\n      placeholder: 'User management functionality will be implemented here.'\n    },\n    ar: {\n      title: 'إدارة المستخدمين',\n      description: 'إدارة المستخدمين والأدوار والصلاحيات عبر المنصة.',\n      placeholder: 'سيتم تنفيذ وظائف إدارة المستخدمين هنا.'\n    }\n  };\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      <div className=\"mb-8\">\n        <h1 className={`text-3xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].title}\n        </h1>\n        <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>\n          {content[language].description}\n        </p>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow-md p-8 text-center\">\n        <div \n          className=\"w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-white text-2xl\"\n          style={{ backgroundColor: 'var(--emerald-green)' }}\n        >\n          👥\n        </div>\n        <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>\n          {content[language].placeholder}\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QACxE,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,aAAa;YACb,aAAa;QACf;QACA,IAAI;YACF,OAAO;YACP,aAAa;YACb,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,aAAa,OAAO,eAAe,aAAa;;0BACjE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAW,CAAC,wBAAwB,EAAE,aAAa,OAAO,gBAAgB,IAAI;wBAAE,OAAO;4BAAE,OAAO;wBAAuB;kCACxH,OAAO,CAAC,SAAS,CAAC,KAAK;;;;;;kCAE1B,8OAAC;wBAAE,WAAW,CAAC,cAAc,EAAE,aAAa,OAAO,gBAAgB,IAAI;kCACpE,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;;;;;;;0BAIlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAuB;kCAClD;;;;;;kCAGD,8OAAC;wBAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,gBAAgB,IAAI;wBAAE,OAAO;4BAAE,OAAO;wBAAuB;kCACvG,OAAO,CAAC,SAAS,CAAC,WAAW;;;;;;;;;;;;;;;;;;AAKxC", "debugId": null}}]}