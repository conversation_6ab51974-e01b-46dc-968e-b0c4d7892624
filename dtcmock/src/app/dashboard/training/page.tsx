'use client';

import { useState, useEffect } from 'react';

export default function TrainingCourses() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'Training Courses',
      description: 'Manage training programs and educational content.',
      placeholder: 'Training course management functionality will be implemented here.'
    },
    ar: {
      title: 'الدورات التدريبية',
      description: 'إدارة البرامج التدريبية والمحتوى التعليمي.',
      placeholder: 'سيتم تنفيذ وظائف إدارة الدورات التدريبية هنا.'
    }
  };

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <div className="mb-8">
        <h1 className={`text-3xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
          {content[language].title}
        </h1>
        <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
          {content[language].description}
        </p>
      </div>

      <div className="bg-white rounded-xl shadow-lg p-8 text-center card-shadow">
        <div
          className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-white"
          style={{ backgroundColor: 'var(--emerald-green)' }}
        >
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        </div>
        <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
          {content[language].placeholder}
        </p>
      </div>
    </div>
  );
}
