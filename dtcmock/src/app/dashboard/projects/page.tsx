'use client';

import { useState, useEffect } from 'react';

export default function Projects() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'Projects',
      description: 'Manage and track digital transformation projects.',
      placeholder: 'Project management functionality will be implemented here.'
    },
    ar: {
      title: 'المشاريع',
      description: 'إدارة وتتبع مشاريع التحول الرقمي.',
      placeholder: 'سيتم تنفيذ وظائف إدارة المشاريع هنا.'
    }
  };

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <div className="mb-8">
        <h1 className={`text-3xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
          {content[language].title}
        </h1>
        <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
          {content[language].description}
        </p>
      </div>

      <div className="bg-white rounded-xl shadow-lg p-8 text-center card-shadow">
        <div
          className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-white"
          style={{ backgroundColor: 'var(--emerald-green)' }}
        >
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
          {content[language].placeholder}
        </p>
      </div>
    </div>
  );
}
