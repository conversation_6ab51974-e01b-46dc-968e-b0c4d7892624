'use client';

import { useState, useEffect } from 'react';

export default function Projects() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'Projects',
      description: 'Manage and track digital transformation projects.',
      placeholder: 'Project management functionality will be implemented here.'
    },
    ar: {
      title: 'المشاريع',
      description: 'إدارة وتتبع مشاريع التحول الرقمي.',
      placeholder: 'سيتم تنفيذ وظائف إدارة المشاريع هنا.'
    }
  };

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <div className="mb-8">
        <h1 className={`text-3xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
          {content[language].title}
        </h1>
        <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
          {content[language].description}
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-md p-8 text-center">
        <div 
          className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-white text-2xl"
          style={{ backgroundColor: 'var(--emerald-green)' }}
        >
          📊
        </div>
        <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
          {content[language].placeholder}
        </p>
      </div>
    </div>
  );
}
