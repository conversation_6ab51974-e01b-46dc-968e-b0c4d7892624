'use client';

import { useState, useEffect } from 'react';
import Hero from '../../../components/Hero';

export default function FrameworkManagement() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'Framework Management',
      subtitle: 'Digital Transformation',
      description: 'Manage digital transformation frameworks, methodologies, and best practices to drive organizational change.',
      placeholder: 'Framework management functionality will be implemented here.'
    },
    ar: {
      title: 'إدارة الإطار',
      subtitle: 'التحول الرقمي',
      description: 'إدارة أطر ومنهجيات وأفضل الممارسات للتحول الرقمي لدفع التغيير التنظيمي.',
      placeholder: 'سيتم تنفيذ وظائف إدارة الإطار هنا.'
    }
  };

  const frameworkIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
    </svg>
  );

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={content[language].title}
        subtitle={content[language].subtitle}
        description={content[language].description}
        icon={frameworkIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: content[language].title }
        ]}
      />

      <div className="bg-white">
        <div className="px-12 py-12">
          {/* Framework Categories */}
          <div className={`flex gap-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>

            {/* Data Management Frameworks Card */}
            <div className="flex-1 group">
              <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 border border-blue-200 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-10">
                  <div className="absolute top-4 right-4 w-32 h-32 rounded-full bg-blue-300"></div>
                  <div className="absolute bottom-4 left-4 w-24 h-24 rounded-lg bg-blue-400 rotate-45"></div>
                  <div className="absolute top-1/2 left-1/3 w-16 h-16 rounded-full bg-blue-200"></div>
                </div>

                <div className="relative p-8">
                  {/* Header */}
                  <div className={`flex items-center mb-6 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                    <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                      </svg>
                    </div>
                    <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>
                      <h3 className={`text-2xl font-bold text-blue-800 ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {language === 'en' ? 'Data Management' : 'إدارة البيانات'}
                      </h3>
                      <p className={`text-blue-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {language === 'en' ? 'Frameworks' : 'الأطر'}
                      </p>
                    </div>
                  </div>

                  {/* Description */}
                  <p className={`text-blue-700 mb-6 leading-relaxed ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`}>
                    {language === 'en'
                      ? 'Comprehensive frameworks for data governance, quality management, and analytics to drive data-driven decision making across your organization.'
                      : 'أطر شاملة لحوكمة البيانات وإدارة الجودة والتحليلات لدفع اتخاذ القرارات المبنية على البيانات عبر مؤسستك.'
                    }
                  </p>

                  {/* Features */}
                  <div className="space-y-3 mb-8">
                    {[
                      { en: 'Data Governance Frameworks', ar: 'أطر حوكمة البيانات' },
                      { en: 'Data Quality Management', ar: 'إدارة جودة البيانات' },
                      { en: 'Analytics & BI Frameworks', ar: 'أطر التحليلات وذكاء الأعمال' },
                      { en: 'Data Security & Privacy', ar: 'أمان البيانات والخصوصية' }
                    ].map((feature, index) => (
                      <div key={index} className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                        <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                        <span className={`text-blue-700 font-medium ${language === 'ar' ? 'mr-3 font-arabic' : 'ml-3'}`}>
                          {feature[language]}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* Action Button */}
                  <button className={`w-full py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-2xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {language === 'en' ? 'Explore Frameworks' : 'استكشاف الأطر'}
                  </button>

                  {/* Stats */}
                  <div className={`flex justify-between mt-6 pt-6 border-t border-blue-300 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                    <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                      <div className={`text-2xl font-bold text-blue-800 ${language === 'ar' ? 'font-arabic' : ''}`}>12</div>
                      <div className={`text-xs text-blue-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {language === 'en' ? 'Frameworks' : 'إطار'}
                      </div>
                    </div>
                    <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                      <div className={`text-2xl font-bold text-blue-800 ${language === 'ar' ? 'font-arabic' : ''}`}>8</div>
                      <div className={`text-xs text-blue-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {language === 'en' ? 'Active' : 'نشط'}
                      </div>
                    </div>
                    <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                      <div className={`text-2xl font-bold text-blue-800 ${language === 'ar' ? 'font-arabic' : ''}`}>95%</div>
                      <div className={`text-xs text-blue-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {language === 'en' ? 'Compliance' : 'الامتثال'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Enterprise Architecture Frameworks Card */}
            <div className="flex-1 group">
              <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-purple-50 via-purple-100 to-purple-200 border border-purple-200 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-10">
                  <div className="absolute top-4 left-4 w-32 h-32 rounded-full bg-purple-300"></div>
                  <div className="absolute bottom-4 right-4 w-24 h-24 rounded-lg bg-purple-400 rotate-45"></div>
                  <div className="absolute top-1/2 right-1/3 w-16 h-16 rounded-full bg-purple-200"></div>
                </div>

                <div className="relative p-8">
                  {/* Header */}
                  <div className={`flex items-center mb-6 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                    <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>
                      <h3 className={`text-2xl font-bold text-purple-800 ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {language === 'en' ? 'Enterprise Architecture' : 'هندسة المؤسسة'}
                      </h3>
                      <p className={`text-purple-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {language === 'en' ? 'Frameworks' : 'الأطر'}
                      </p>
                    </div>
                  </div>

                  {/* Description */}
                  <p className={`text-purple-700 mb-6 leading-relaxed ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`}>
                    {language === 'en'
                      ? 'Strategic frameworks for enterprise architecture design, implementation, and governance to align technology with business objectives and drive digital transformation.'
                      : 'أطر استراتيجية لتصميم وتنفيذ وحوكمة هندسة المؤسسة لمواءمة التكنولوجيا مع أهداف الأعمال ودفع التحول الرقمي.'
                    }
                  </p>

                  {/* Features */}
                  <div className="space-y-3 mb-8">
                    {[
                      { en: 'TOGAF & ArchiMate', ar: 'TOGAF و ArchiMate' },
                      { en: 'Business Architecture', ar: 'هندسة الأعمال' },
                      { en: 'Technology Architecture', ar: 'هندسة التكنولوجيا' },
                      { en: 'Solution Architecture', ar: 'هندسة الحلول' }
                    ].map((feature, index) => (
                      <div key={index} className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                        <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                        <span className={`text-purple-700 font-medium ${language === 'ar' ? 'mr-3 font-arabic' : 'ml-3'}`}>
                          {feature[language]}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* Action Button */}
                  <button className={`w-full py-4 bg-gradient-to-r from-purple-500 to-purple-600 text-white font-semibold rounded-2xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {language === 'en' ? 'Explore Frameworks' : 'استكشاف الأطر'}
                  </button>

                  {/* Stats */}
                  <div className={`flex justify-between mt-6 pt-6 border-t border-purple-300 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                    <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                      <div className={`text-2xl font-bold text-purple-800 ${language === 'ar' ? 'font-arabic' : ''}`}>15</div>
                      <div className={`text-xs text-purple-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {language === 'en' ? 'Frameworks' : 'إطار'}
                      </div>
                    </div>
                    <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                      <div className={`text-2xl font-bold text-purple-800 ${language === 'ar' ? 'font-arabic' : ''}`}>11</div>
                      <div className={`text-xs text-purple-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {language === 'en' ? 'Active' : 'نشط'}
                      </div>
                    </div>
                    <div className={`text-center ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                      <div className={`text-2xl font-bold text-purple-800 ${language === 'ar' ? 'font-arabic' : ''}`}>88%</div>
                      <div className={`text-xs text-purple-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {language === 'en' ? 'Maturity' : 'النضج'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="mt-12 p-8 bg-gradient-to-r from-gray-50 to-gray-100 rounded-3xl border border-gray-200">
            <div className={`text-center mb-8 ${language === 'ar' ? 'text-right' : 'text-left'}`}>
              <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                {language === 'en' ? 'Framework Implementation Journey' : 'رحلة تنفيذ الأطر'}
              </h3>
              <p className={`text-gray-600 max-w-4xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>
                {language === 'en'
                  ? 'Our comprehensive framework management approach ensures successful digital transformation through structured methodologies, best practices, and continuous improvement processes.'
                  : 'نهجنا الشامل لإدارة الأطر يضمن نجاح التحول الرقمي من خلال المنهجيات المنظمة وأفضل الممارسات وعمليات التحسين المستمر.'
                }
              </p>
            </div>

            {/* Process Steps */}
            <div className={`flex justify-between items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
              {[
                { en: 'Assessment', ar: 'التقييم', icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2' },
                { en: 'Design', ar: 'التصميم', icon: 'M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z' },
                { en: 'Implementation', ar: 'التنفيذ', icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z' },
                { en: 'Optimization', ar: 'التحسين', icon: 'M13 10V3L4 14h7v7l9-11h-7z' }
              ].map((step, index) => (
                <div key={index} className={`flex flex-col items-center ${language === 'ar' ? 'text-right' : 'text-center'}`}>
                  <div
                    className="w-16 h-16 rounded-2xl flex items-center justify-center text-white mb-4 shadow-lg"
                    style={{ backgroundColor: 'var(--emerald-green)' }}
                  >
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={step.icon} />
                    </svg>
                  </div>
                  <h4 className={`font-bold text-lg ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    {step[language]}
                  </h4>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
