'use client';

import { useState, useEffect } from 'react';
import Hero from '../../../components/Hero';
import DAMAWheel from '../../../components/DAMAWheel';
import EALayers from '../../../components/EALayers';
import SpecificationView from '../../../components/SpecificationView';

interface Framework {
  id: string;
  name: string;
  country: 'saudi' | 'qatar';
  type: 'data-management' | 'enterprise-architecture';
}

const frameworks: Framework[] = [
  { id: 'ndmo', name: 'NDMO', country: 'saudi', type: 'data-management' },
  { id: 'npc', name: 'NPC', country: 'qatar', type: 'data-management' },
  { id: 'noura', name: 'NOURA', country: 'saudi', type: 'enterprise-architecture' },
  { id: 'gea', name: 'GEA', country: 'qatar', type: 'enterprise-architecture' }
];

export default function FrameworkManagement() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  const [selectedCategory, setSelectedCategory] = useState<'main' | 'data-management' | 'enterprise-architecture'>('main');
  const [selectedFramework, setSelectedFramework] = useState<string | null>(null);
  const [selectedDomain, setSelectedDomain] = useState<string | null>(null);

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'Framework Management',
      subtitle: 'Digital Transformation',
      description: 'Manage digital transformation frameworks, methodologies, and best practices to drive organizational change.',
      placeholder: 'Framework management functionality will be implemented here.'
    },
    ar: {
      title: 'إدارة الإطار',
      subtitle: 'التحول الرقمي',
      description: 'إدارة أطر ومنهجيات وأفضل الممارسات للتحول الرقمي لدفع التغيير التنظيمي.',
      placeholder: 'سيتم تنفيذ وظائف إدارة الإطار هنا.'
    }
  };

  const frameworkIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
    </svg>
  );

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={content[language].title}
        subtitle={content[language].subtitle}
        description={content[language].description}
        icon={frameworkIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: content[language].title }
        ]}
      />

      <div className="bg-white">
        {selectedCategory === 'main' && (
          <div className="px-12 py-20">
            {/* Two Larger Enhanced Cards */}
            <div className={`flex gap-16 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>

              {/* Data Management Card - Enhanced */}
              <div className="flex-1 group cursor-pointer" onClick={() => setSelectedCategory('data-management')}>
                <div className="relative overflow-hidden rounded-3xl h-96 transition-all duration-700 ease-out transform hover:scale-105 hover:shadow-2xl">
                  {/* Enhanced Background with Pattern */}
                  <div
                    className="absolute inset-0 transition-all duration-700 ease-out"
                    style={{
                      background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)'
                    }}
                  ></div>

                  {/* Animated Background Pattern */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute top-8 right-8 w-32 h-32 rounded-full bg-white animate-pulse-subtle"></div>
                    <div className="absolute bottom-8 left-8 w-24 h-24 rounded-lg bg-white rotate-45 animate-pulse-subtle" style={{ animationDelay: '1s' }}></div>
                    <div className="absolute top-1/2 left-1/3 w-16 h-16 rounded-full bg-white animate-pulse-subtle" style={{ animationDelay: '2s' }}></div>
                  </div>

                  {/* Blade Effect - Enhanced */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                    <div
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"
                      style={{ width: '200%' }}
                    ></div>
                  </div>

                  {/* Enhanced Content */}
                  <div className="relative h-full flex flex-col justify-center items-center text-center p-12">
                    <div className="w-24 h-24 rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-500 shadow-2xl">
                      <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                      </svg>
                    </div>
                    <h3 className={`text-4xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>
                      {language === 'en' ? 'Data Management' : 'إدارة البيانات'}
                    </h3>
                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>
                      {language === 'en' ? 'Frameworks & Standards' : 'الأطر والمعايير'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Enterprise Architecture Card - Enhanced */}
              <div className="flex-1 group cursor-pointer" onClick={() => setSelectedCategory('enterprise-architecture')}>
                <div className="relative overflow-hidden rounded-3xl h-96 transition-all duration-700 ease-out transform hover:scale-105 hover:shadow-2xl">
                  {/* Enhanced Background with Pattern */}
                  <div
                    className="absolute inset-0 transition-all duration-700 ease-out"
                    style={{
                      background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)'
                    }}
                  ></div>

                  {/* Animated Background Pattern */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute top-8 left-8 w-32 h-32 rounded-full bg-white animate-pulse-subtle"></div>
                    <div className="absolute bottom-8 right-8 w-24 h-24 rounded-lg bg-white rotate-45 animate-pulse-subtle" style={{ animationDelay: '1s' }}></div>
                    <div className="absolute top-1/2 right-1/3 w-16 h-16 rounded-full bg-white animate-pulse-subtle" style={{ animationDelay: '2s' }}></div>
                  </div>

                  {/* Blade Effect - Enhanced */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                    <div
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"
                      style={{ width: '200%' }}
                    ></div>
                  </div>

                  {/* Enhanced Content */}
                  <div className="relative h-full flex flex-col justify-center items-center text-center p-12">
                    <div className="w-24 h-24 rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-500 shadow-2xl">
                      <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <h3 className={`text-4xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>
                      {language === 'en' ? 'Enterprise Architecture' : 'هندسة المؤسسة'}
                    </h3>
                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>
                      {language === 'en' ? 'Frameworks & Models' : 'الأطر والنماذج'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Data Management Frameworks */}
        {selectedCategory === 'data-management' && (
          <div className="px-12 py-16">
            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
              <button
                onClick={() => setSelectedCategory('main')}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                style={{ color: 'var(--emerald-green)' }}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />
                </svg>
              </button>
              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>
                {language === 'en' ? 'Data Management Frameworks' : 'أطر إدارة البيانات'}
              </h2>
            </div>

            <div className={`flex gap-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
              {/* NDMO Card */}
              <div className="flex-1 group cursor-pointer" onClick={() => setSelectedFramework('ndmo')}>
                <div className="relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl">
                  <div
                    className="absolute inset-0"
                    style={{ background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)' }}
                  ></div>

                  {/* Saudi Flag */}
                  <div className="absolute top-4 right-4 px-3 py-1 bg-green-600 text-white text-xs font-bold rounded-full flex items-center gap-2">
                    <span>🇸🇦</span>
                    <span>{language === 'en' ? 'Saudi Arabia' : 'السعودية'}</span>
                  </div>

                  <div className="relative h-full flex flex-col justify-center items-center text-center p-8">
                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>
                      NDMO
                    </h3>
                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>
                      {language === 'en' ? 'National Data Management Office' : 'مكتب إدارة البيانات الوطني'}
                    </p>
                  </div>
                </div>
              </div>

              {/* NPC Card */}
              <div className="flex-1 group cursor-pointer" onClick={() => setSelectedFramework('npc')}>
                <div className="relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl">
                  <div
                    className="absolute inset-0"
                    style={{ background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)' }}
                  ></div>

                  {/* Qatar Flag */}
                  <div className="absolute top-4 right-4 px-3 py-1 bg-red-600 text-white text-xs font-bold rounded-full flex items-center gap-2">
                    <span>🇶🇦</span>
                    <span>{language === 'en' ? 'Qatar' : 'قطر'}</span>
                  </div>

                  <div className="relative h-full flex flex-col justify-center items-center text-center p-8">
                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>
                      NPC
                    </h3>
                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>
                      {language === 'en' ? 'National Planning Council' : 'مجلس التخطيط الوطني'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Enterprise Architecture Frameworks */}
        {selectedCategory === 'enterprise-architecture' && (
          <div className="px-12 py-16">
            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
              <button
                onClick={() => setSelectedCategory('main')}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                style={{ color: 'var(--emerald-green)' }}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />
                </svg>
              </button>
              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>
                {language === 'en' ? 'Enterprise Architecture Frameworks' : 'أطر هندسة المؤسسة'}
              </h2>
            </div>

            <div className={`flex gap-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
              {/* NOURA Card */}
              <div className="flex-1 group cursor-pointer" onClick={() => setSelectedFramework('noura')}>
                <div className="relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl">
                  <div
                    className="absolute inset-0"
                    style={{ background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)' }}
                  ></div>

                  {/* Saudi Flag */}
                  <div className="absolute top-4 right-4 px-3 py-1 bg-green-600 text-white text-xs font-bold rounded-full flex items-center gap-2">
                    <span>🇸🇦</span>
                    <span>{language === 'en' ? 'Saudi Arabia' : 'السعودية'}</span>
                  </div>

                  <div className="relative h-full flex flex-col justify-center items-center text-center p-8">
                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>
                      NOURA
                    </h3>
                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>
                      {language === 'en' ? 'National Enterprise Architecture' : 'هندسة المؤسسة الوطنية'}
                    </p>
                  </div>
                </div>
              </div>

              {/* GEA Card */}
              <div className="flex-1 group cursor-pointer" onClick={() => setSelectedFramework('gea')}>
                <div className="relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl">
                  <div
                    className="absolute inset-0"
                    style={{ background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)' }}
                  ></div>

                  {/* Qatar Flag */}
                  <div className="absolute top-4 right-4 px-3 py-1 bg-red-600 text-white text-xs font-bold rounded-full flex items-center gap-2">
                    <span>🇶🇦</span>
                    <span>{language === 'en' ? 'Qatar' : 'قطر'}</span>
                  </div>

                  <div className="relative h-full flex flex-col justify-center items-center text-center p-8">
                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>
                      GEA
                    </h3>
                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>
                      {language === 'en' ? 'Government Enterprise Architecture' : 'هندسة المؤسسة الحكومية'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Framework Detail Views */}
        {selectedFramework && (
          <div className="px-12 py-16">
            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
              <button
                onClick={() => setSelectedFramework(null)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                style={{ color: 'var(--emerald-green)' }}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />
                </svg>
              </button>
              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>
                {selectedFramework.toUpperCase()}
              </h2>

              {/* Country Tag */}
              <div className={`px-3 py-1 rounded-full text-white text-sm font-bold flex items-center gap-2 ${language === 'ar' ? 'mr-4' : 'ml-4'}`}
                style={{
                  backgroundColor: frameworks.find(f => f.id === selectedFramework)?.country === 'saudi' ? '#16a34a' : '#dc2626'
                }}
              >
                <span>{frameworks.find(f => f.id === selectedFramework)?.country === 'saudi' ? '🇸🇦' : '🇶🇦'}</span>
                <span>
                  {frameworks.find(f => f.id === selectedFramework)?.country === 'saudi'
                    ? (language === 'en' ? 'Saudi Arabia' : 'السعودية')
                    : (language === 'en' ? 'Qatar' : 'قطر')
                  }
                </span>
              </div>
            </div>

            {/* DAMA Wheel for Data Management Frameworks */}
            {(selectedFramework === 'ndmo' || selectedFramework === 'npc') && !selectedDomain && (
              <div className="bg-white rounded-3xl shadow-lg p-8">
                <DAMAWheel
                  language={language}
                  onDomainClick={(domain) => {
                    setSelectedDomain(domain);
                  }}
                />
              </div>
            )}

            {/* EA Layers for Enterprise Architecture Frameworks */}
            {(selectedFramework === 'noura' || selectedFramework === 'gea') && !selectedDomain && (
              <div className="bg-white rounded-3xl shadow-lg p-8">
                <EALayers
                  language={language}
                  onLayerClick={(layer) => {
                    setSelectedDomain(layer);
                  }}
                />
              </div>
            )}

            {/* Specification View */}
            {selectedDomain && selectedFramework && (
              <div className="bg-white rounded-3xl shadow-lg">
                <SpecificationView
                  framework={selectedFramework}
                  domain={selectedDomain}
                  language={language}
                  onBack={() => setSelectedDomain(null)}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
