'use client';

import { useState, useEffect } from 'react';
import Hero from '../../../components/Hero';

export default function FrameworkManagement() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'Framework Management',
      subtitle: 'Digital Transformation',
      description: 'Manage digital transformation frameworks, methodologies, and best practices to drive organizational change.',
      placeholder: 'Framework management functionality will be implemented here.'
    },
    ar: {
      title: 'إدارة الإطار',
      subtitle: 'التحول الرقمي',
      description: 'إدارة أطر ومنهجيات وأفضل الممارسات للتحول الرقمي لدفع التغيير التنظيمي.',
      placeholder: 'سيتم تنفيذ وظائف إدارة الإطار هنا.'
    }
  };

  const frameworkIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
    </svg>
  );

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={content[language].title}
        subtitle={content[language].subtitle}
        description={content[language].description}
        icon={frameworkIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: content[language].title }
        ]}
      />

      <div className="bg-white">
        <div className="px-12 py-16">
          {/* Two Super Clean Cards */}
          <div className={`flex gap-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>

            {/* Data Management Card */}
            <div className="flex-1 group cursor-pointer">
              <div className="relative overflow-hidden rounded-3xl h-80 transition-all duration-700 ease-out transform hover:scale-105 hover:shadow-2xl">
                {/* Blade Animation Background */}
                <div
                  className="absolute inset-0 bg-gradient-to-br transition-all duration-700 ease-out"
                  style={{
                    background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)'
                  }}
                ></div>

                {/* Blade Effect - Animated Sweep */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                  <div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"
                    style={{ width: '200%' }}
                  ></div>
                </div>

                {/* Content */}
                <div className="relative h-full flex flex-col justify-center items-center text-center p-8">
                  <div className="w-20 h-20 rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-500">
                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                    </svg>
                  </div>
                  <h3 className={`text-3xl font-bold text-white mb-2 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {language === 'en' ? 'Data Management' : 'إدارة البيانات'}
                  </h3>
                </div>
              </div>
            </div>

            {/* Enterprise Architecture Card */}
            <div className="flex-1 group cursor-pointer">
              <div className="relative overflow-hidden rounded-3xl h-80 transition-all duration-700 ease-out transform hover:scale-105 hover:shadow-2xl">
                {/* Blade Animation Background */}
                <div
                  className="absolute inset-0 bg-gradient-to-br transition-all duration-700 ease-out"
                  style={{
                    background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)'
                  }}
                ></div>

                {/* Blade Effect - Animated Sweep */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                  <div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"
                    style={{ width: '200%' }}
                  ></div>
                </div>

                {/* Content */}
                <div className="relative h-full flex flex-col justify-center items-center text-center p-8">
                  <div className="w-20 h-20 rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-500">
                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <h3 className={`text-3xl font-bold text-white mb-2 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {language === 'en' ? 'Enterprise Architecture' : 'هندسة المؤسسة'}
                  </h3>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
