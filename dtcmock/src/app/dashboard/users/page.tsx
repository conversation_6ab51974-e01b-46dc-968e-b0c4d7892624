'use client';

import { useState, useEffect } from 'react';
import Hero from '../../../components/Hero';

export default function UserManagement() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'User Management',
      subtitle: 'Administration',
      description: 'Manage users, roles, and permissions across the platform with comprehensive control and oversight.',
      placeholder: 'User management functionality will be implemented here.'
    },
    ar: {
      title: 'إدارة المستخدمين',
      subtitle: 'الإدارة',
      description: 'إدارة المستخدمين والأدوار والصلاحيات عبر المنصة مع التحكم والإشراف الشامل.',
      placeholder: 'سيتم تنفيذ وظائف إدارة المستخدمين هنا.'
    }
  };

  const userIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
    </svg>
  );

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      {/* Hero Section */}
      <Hero
        title={content[language].title}
        subtitle={content[language].subtitle}
        description={content[language].description}
        icon={userIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: content[language].title }
        ]}
      />

      {/* Main Content - Seamless */}
      <div className="bg-white">
        <div className="px-12 py-16 text-center">
          <div
            className="w-24 h-24 mx-auto mb-8 rounded-3xl flex items-center justify-center text-white shadow-2xl"
            style={{ backgroundColor: 'var(--emerald-green)' }}
          >
            <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
          <h2 className={`text-3xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
            {language === 'en' ? 'Coming Soon' : 'قريباً'}
          </h2>
          <p className={`text-xl text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : ''}`}>
            {content[language].placeholder}
          </p>
        </div>
      </div>
    </div>
  );
}
