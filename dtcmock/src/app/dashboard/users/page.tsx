'use client';

import { useState, useEffect } from 'react';

export default function UserManagement() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'User Management',
      description: 'Manage users, roles, and permissions across the platform.',
      placeholder: 'User management functionality will be implemented here.'
    },
    ar: {
      title: 'إدارة المستخدمين',
      description: 'إدارة المستخدمين والأدوار والصلاحيات عبر المنصة.',
      placeholder: 'سيتم تنفيذ وظائف إدارة المستخدمين هنا.'
    }
  };

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <div className="mb-8">
        <h1 className={`text-3xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
          {content[language].title}
        </h1>
        <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : ''}`}>
          {content[language].description}
        </p>
      </div>

      <div className="bg-white rounded-xl shadow-lg p-8 text-center card-shadow">
        <div
          className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-white"
          style={{ backgroundColor: 'var(--emerald-green)' }}
        >
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
        </div>
        <p className={`text-lg ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
          {content[language].placeholder}
        </p>
      </div>
    </div>
  );
}
