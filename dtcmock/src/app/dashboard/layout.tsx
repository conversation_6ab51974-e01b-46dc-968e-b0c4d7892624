'use client';

import { useState, useEffect } from 'react';
import Sidebar from '../../components/Sidebar';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [userRole, setUserRole] = useState<'admin' | 'consultant' | 'project-manager' | 'trainee'>('admin');
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  // Mock: Get user data from localStorage or context
  useEffect(() => {
    // In a real app, this would come from authentication context or API
    const mockUserRole = localStorage.getItem('userRole') as 'admin' | 'consultant' | 'project-manager' | 'trainee' || 'admin';
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    
    setUserRole(mockUserRole);
    setLanguage(mockLanguage);
  }, []);

  const handleLanguageChange = (newLanguage: 'en' | 'ar') => {
    setLanguage(newLanguage);
    localStorage.setItem('language', newLanguage);
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${language === 'ar' ? 'rtl' : 'ltr'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Sidebar */}
      <Sidebar 
        userRole={userRole} 
        language={language} 
        onLanguageChange={handleLanguageChange}
      />
      
      {/* Main Content */}
      <div className={`sidebar-transition ${language === 'ar' ? 'mr-64' : 'ml-64'}`}>
        <main className="p-6 dashboard-content">
          {children}
        </main>
      </div>
    </div>
  );
}
