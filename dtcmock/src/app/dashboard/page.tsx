'use client';

import { useState, useEffect } from 'react';
import Hero from '../../components/Hero';

export default function DashboardHome() {
  const [userRole, setUserRole] = useState<'admin' | 'consultant' | 'project-manager' | 'trainee'>('admin');
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  useEffect(() => {
    const mockUserRole = localStorage.getItem('userRole') as 'admin' | 'consultant' | 'project-manager' | 'trainee' || 'admin';
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    
    setUserRole(mockUserRole);
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      welcome: 'Welcome to DTC Accelerator',
      adminDashboard: 'Admin Dashboard',
      consultantDashboard: 'Consultant Dashboard',
      projectManagerDashboard: 'Project Manager Dashboard',
      traineeDashboard: 'Trainee Dashboard',
      overview: 'Dashboard Overview',
      description: 'This is your personalized dashboard based on your role and permissions.',
      quickStats: 'Quick Statistics',
      recentActivity: 'Recent Activity'
    },
    ar: {
      welcome: 'مرحباً بك في مسرع التحول الرقمي',
      adminDashboard: 'لوحة تحكم المدير',
      consultantDashboard: 'لوحة تحكم المستشار',
      projectManagerDashboard: 'لوحة تحكم مدير المشروع',
      traineeDashboard: 'لوحة تحكم المتدرب',
      overview: 'نظرة عامة على لوحة التحكم',
      description: 'هذه لوحة التحكم الشخصية الخاصة بك بناءً على دورك وصلاحياتك.',
      quickStats: 'إحصائيات سريعة',
      recentActivity: 'النشاط الأخير'
    }
  };

  const getDashboardTitle = () => {
    switch (userRole) {
      case 'admin':
        return content[language].adminDashboard;
      case 'consultant':
        return content[language].consultantDashboard;
      case 'project-manager':
        return content[language].projectManagerDashboard;
      case 'trainee':
        return content[language].traineeDashboard;
      default:
        return content[language].adminDashboard;
    }
  };

  const getStatsForRole = () => {
    switch (userRole) {
      case 'admin':
        return [
          { label: language === 'en' ? 'Total Users' : 'إجمالي المستخدمين', value: '1,234', icon: '👥' },
          { label: language === 'en' ? 'Active Projects' : 'المشاريع النشطة', value: '56', icon: '📊' },
          { label: language === 'en' ? 'Frameworks' : 'الأطر', value: '12', icon: '🏗️' },
          { label: language === 'en' ? 'Training Courses' : 'الدورات التدريبية', value: '89', icon: '🎓' }
        ];
      case 'consultant':
        return [
          { label: language === 'en' ? 'My Projects' : 'مشاريعي', value: '8', icon: '📊' },
          { label: language === 'en' ? 'Consultations' : 'الاستشارات', value: '24', icon: '💼' },
          { label: language === 'en' ? 'Completed Tasks' : 'المهام المكتملة', value: '156', icon: '✅' }
        ];
      case 'project-manager':
        return [
          { label: language === 'en' ? 'Managed Projects' : 'المشاريع المدارة', value: '12', icon: '📊' },
          { label: language === 'en' ? 'Team Members' : 'أعضاء الفريق', value: '45', icon: '👥' },
          { label: language === 'en' ? 'Milestones' : 'المعالم', value: '78', icon: '🎯' }
        ];
      case 'trainee':
        return [
          { label: language === 'en' ? 'Enrolled Courses' : 'الدورات المسجلة', value: '6', icon: '🎓' },
          { label: language === 'en' ? 'Completed Modules' : 'الوحدات المكتملة', value: '23', icon: '✅' },
          { label: language === 'en' ? 'Certificates' : 'الشهادات', value: '3', icon: '🏆' }
        ];
      default:
        return [];
    }
  };

  const getHeroIcon = () => {
    switch (userRole) {
      case 'admin':
        return (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        );
      case 'consultant':
        return (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6" />
          </svg>
        );
      case 'project-manager':
        return (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        );
      case 'trainee':
        return (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        );
      default:
        return (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
        );
    }
  };

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      {/* Hero Section */}
      <Hero
        title={content[language].welcome}
        subtitle={getDashboardTitle()}
        description={content[language].description}
        icon={getHeroIcon()}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم' },
          { label: language === 'en' ? 'Home' : 'الرئيسية' }
        ]}
      />

      {/* Overview Card */}
      <div className="bg-white rounded-xl shadow-lg p-8 mb-8 card-shadow">
        <h3 className={`text-xl font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
          {content[language].overview}
        </h3>
        <p className={`text-gray-600 leading-relaxed ${language === 'ar' ? 'font-arabic' : ''}`}>
          {content[language].description}
        </p>
      </div>

      {/* Quick Statistics */}
      <div className="mb-8">
        <h3 className={`text-lg font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
          {content[language].quickStats}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {getStatsForRole().map((stat, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-6 card-shadow hover:shadow-xl transition-all duration-300">
              <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                <div
                  className="w-14 h-14 rounded-xl flex items-center justify-center text-white shadow-lg"
                  style={{ backgroundColor: 'var(--emerald-green)' }}
                >
                  <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>
                  <p className={`text-3xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                    {stat.value}
                  </p>
                  <p className={`text-sm text-gray-600 font-medium ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {stat.label}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-xl shadow-lg p-8 card-shadow">
        <h3 className={`text-xl font-semibold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
          {content[language].recentActivity}
        </h3>
        <div className="space-y-4">
          {[1, 2, 3].map((item) => (
            <div key={item} className={`flex items-center p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
              <div
                className="w-10 h-10 rounded-xl flex items-center justify-center text-white shadow-sm"
                style={{ backgroundColor: 'var(--emerald-green)' }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className={`${language === 'ar' ? 'mr-4 text-right' : 'ml-4 text-left'}`}>
                <p className={`text-sm font-semibold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {language === 'en' ? `Recent activity item ${item}` : `عنصر النشاط الأخير ${item}`}
                </p>
                <p className={`text-xs text-gray-500 mt-1 ${language === 'ar' ? 'font-arabic' : ''}`}>
                  {language === 'en' ? '2 hours ago' : 'منذ ساعتين'}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
