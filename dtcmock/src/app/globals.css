@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Custom Business Color Palette */
  --emerald-green: #026c4a;
  --deep-emerald: #0c402e;
  --black: #010101;
  --charcoal-grey: #272626;
  --white: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Custom animations for professional feel */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.animate-pulse-subtle {
  animation: pulse-subtle 3s ease-in-out infinite;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .form-input {
  text-align: right;
}

/* Arabic Font Support */
.font-arabic {
  font-family: 'Segoe UI', Tahoma, Arial, Helvetica, sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

/* RTL specific improvements */
[dir="rtl"] input,
[dir="rtl"] select,
[dir="rtl"] textarea {
  text-align: right;
  padding-right: 1rem;
  padding-left: 1rem;
}

[dir="rtl"] button {
  text-align: center;
}

/* RTL animations */
[dir="rtl"] .animate-slide-in-left {
  animation: slideInRight 0.8s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* RTL form improvements */
[dir="rtl"] label {
  text-align: right;
  display: block;
}

[dir="rtl"] .text-center {
  text-align: center;
}

/* Sidebar specific styles */
.sidebar-transition {
  transition: margin-left 0.3s ease, margin-right 0.3s ease;
}

/* Responsive sidebar adjustments */
@media (max-width: 768px) {
  .sidebar-mobile {
    transform: translateX(-100%);
  }

  [dir="rtl"] .sidebar-mobile {
    transform: translateX(100%);
  }

  .sidebar-mobile.open {
    transform: translateX(0);
  }
}

/* Ensure proper spacing for dashboard content */
.dashboard-content {
  min-height: calc(100vh - 2rem);
}

/* Professional hover effects */
.nav-item:hover {
  background-color: rgba(2, 108, 74, 0.1);
  transform: translateX(2px);
}

[dir="rtl"] .nav-item:hover {
  transform: translateX(-2px);
}

/* Smooth transitions for all interactive elements */
.nav-item,
.sidebar-button {
  transition: all 0.2s ease-in-out;
}