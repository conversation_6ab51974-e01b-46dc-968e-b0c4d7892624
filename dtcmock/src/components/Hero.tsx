'use client';

import { useState, useEffect } from 'react';

interface HeroProps {
  title: string;
  subtitle?: string;
  description?: string;
  icon?: React.ReactNode;
  breadcrumbs?: Array<{ label: string; href?: string }>;
}

export default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  return (
    <div 
      className={`relative overflow-hidden mb-8 ${language === 'ar' ? 'text-right' : 'text-left'}`}
      style={{
        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',
      }}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" strokeWidth="0.5"/>
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>
      </div>

      {/* Geometric Shapes */}
      <div className="absolute inset-0 overflow-hidden">
        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>
        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>
        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>
      </div>

      {/* Content */}
      <div className="relative px-8 py-12">
        {/* Breadcrumbs */}
        {breadcrumbs && breadcrumbs.length > 0 && (
          <nav className="mb-6">
            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>
              {breadcrumbs.map((crumb, index) => (
                <li key={index} className="flex items-center">
                  {index > 0 && (
                    <svg 
                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  )}
                  {crumb.href ? (
                    <a 
                      href={crumb.href} 
                      className="hover:text-white transition-colors font-medium"
                    >
                      {crumb.label}
                    </a>
                  ) : (
                    <span className="text-white font-medium">{crumb.label}</span>
                  )}
                </li>
              ))}
            </ol>
          </nav>
        )}

        {/* Main Content */}
        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
          {/* Icon */}
          {icon && (
            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>
              <div className="w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg">
                <div className="text-white">
                  {icon}
                </div>
              </div>
            </div>
          )}

          {/* Text Content */}
          <div className="flex-1">
            {/* Subtitle */}
            {subtitle && (
              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>
                {subtitle}
              </p>
            )}

            {/* Title */}
            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>
              {title}
            </h1>

            {/* Description */}
            {description && (
              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>
                {description}
              </p>
            )}
          </div>
        </div>

        {/* Bottom Accent Line */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20"></div>
      </div>

      {/* Animated Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle"></div>
    </div>
  );
}
