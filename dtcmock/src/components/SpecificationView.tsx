'use client';

import { useState, useEffect } from 'react';

interface SpecificationViewProps {
  framework: string;
  domain: string;
  language: 'en' | 'ar';
  onBack: () => void;
}

export default function SpecificationView({ framework, domain, language, onBack }: SpecificationViewProps) {
  const [activeSection, setActiveSection] = useState('overview');

  const specifications = {
    'data-governance': {
      en: {
        title: 'Data Governance Specifications',
        overview: 'Comprehensive framework for establishing data governance policies, procedures, and organizational structures.',
        sections: [
          {
            id: 'policies',
            title: 'Governance Policies',
            content: 'Define data ownership, stewardship roles, and decision-making authority across the organization.'
          },
          {
            id: 'standards',
            title: 'Data Standards',
            content: 'Establish consistent data definitions, formats, and quality requirements.'
          },
          {
            id: 'compliance',
            title: 'Compliance Framework',
            content: 'Ensure adherence to regulatory requirements and industry standards.'
          }
        ]
      },
      ar: {
        title: 'مواصفات حوكمة البيانات',
        overview: 'إطار شامل لوضع سياسات وإجراءات وهياكل تنظيمية لحوكمة البيانات.',
        sections: [
          {
            id: 'policies',
            title: 'سياسات الحوكمة',
            content: 'تحديد ملكية البيانات وأدوار الإشراف وسلطة اتخاذ القرار عبر المؤسسة.'
          },
          {
            id: 'standards',
            title: 'معايير البيانات',
            content: 'وضع تعريفات وتنسيقات ومتطلبات جودة متسقة للبيانات.'
          },
          {
            id: 'compliance',
            title: 'إطار الامتثال',
            content: 'ضمان الالتزام بالمتطلبات التنظيمية ومعايير الصناعة.'
          }
        ]
      }
    },
    'data-architecture': {
      en: {
        title: 'Data Architecture Specifications',
        overview: 'Technical blueprint for data systems, integration patterns, and infrastructure design.',
        sections: [
          {
            id: 'design',
            title: 'Architecture Design',
            content: 'Define data flow patterns, system interfaces, and integration architectures.'
          },
          {
            id: 'infrastructure',
            title: 'Infrastructure Requirements',
            content: 'Specify hardware, software, and network requirements for data systems.'
          },
          {
            id: 'security',
            title: 'Security Architecture',
            content: 'Implement data protection, access controls, and encryption standards.'
          }
        ]
      },
      ar: {
        title: 'مواصفات هندسة البيانات',
        overview: 'مخطط تقني لأنظمة البيانات وأنماط التكامل وتصميم البنية التحتية.',
        sections: [
          {
            id: 'design',
            title: 'تصميم الهندسة',
            content: 'تحديد أنماط تدفق البيانات وواجهات النظام وهندسة التكامل.'
          },
          {
            id: 'infrastructure',
            title: 'متطلبات البنية التحتية',
            content: 'تحديد متطلبات الأجهزة والبرمجيات والشبكة لأنظمة البيانات.'
          },
          {
            id: 'security',
            title: 'هندسة الأمان',
            content: 'تنفيذ حماية البيانات وضوابط الوصول ومعايير التشفير.'
          }
        ]
      }
    },
    'business': {
      en: {
        title: 'Business Architecture Specifications',
        overview: 'Strategic framework defining business capabilities, processes, and organizational structure.',
        sections: [
          {
            id: 'capabilities',
            title: 'Business Capabilities',
            content: 'Define core business functions and their relationships across the enterprise.'
          },
          {
            id: 'processes',
            title: 'Process Architecture',
            content: 'Map business processes, workflows, and operational procedures.'
          },
          {
            id: 'organization',
            title: 'Organizational Design',
            content: 'Structure roles, responsibilities, and reporting relationships.'
          }
        ]
      },
      ar: {
        title: 'مواصفات هندسة الأعمال',
        overview: 'إطار استراتيجي يحدد قدرات الأعمال والعمليات والهيكل التنظيمي.',
        sections: [
          {
            id: 'capabilities',
            title: 'قدرات الأعمال',
            content: 'تحديد وظائف الأعمال الأساسية وعلاقاتها عبر المؤسسة.'
          },
          {
            id: 'processes',
            title: 'هندسة العمليات',
            content: 'رسم خريطة العمليات التجارية وتدفقات العمل والإجراءات التشغيلية.'
          },
          {
            id: 'organization',
            title: 'التصميم التنظيمي',
            content: 'هيكلة الأدوار والمسؤوليات وعلاقات التقارير.'
          }
        ]
      }
    },
    'application': {
      en: {
        title: 'Application Architecture Specifications',
        overview: 'Technical framework for application design, integration, and lifecycle management.',
        sections: [
          {
            id: 'design',
            title: 'Application Design',
            content: 'Define application components, interfaces, and integration patterns.'
          },
          {
            id: 'integration',
            title: 'Integration Architecture',
            content: 'Specify APIs, messaging, and data exchange mechanisms.'
          },
          {
            id: 'lifecycle',
            title: 'Lifecycle Management',
            content: 'Manage application development, deployment, and maintenance processes.'
          }
        ]
      },
      ar: {
        title: 'مواصفات هندسة التطبيقات',
        overview: 'إطار تقني لتصميم التطبيقات والتكامل وإدارة دورة الحياة.',
        sections: [
          {
            id: 'design',
            title: 'تصميم التطبيق',
            content: 'تحديد مكونات التطبيق والواجهات وأنماط التكامل.'
          },
          {
            id: 'integration',
            title: 'هندسة التكامل',
            content: 'تحديد واجهات برمجة التطبيقات والرسائل وآليات تبادل البيانات.'
          },
          {
            id: 'lifecycle',
            title: 'إدارة دورة الحياة',
            content: 'إدارة عمليات تطوير التطبيقات ونشرها وصيانتها.'
          }
        ]
      }
    }
  };

  const currentSpec = specifications[domain as keyof typeof specifications]?.[language];

  if (!currentSpec) {
    return (
      <div className="p-8 text-center">
        <p className={`text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>
          {language === 'en' ? 'Specification not available' : 'المواصفات غير متوفرة'}
        </p>
      </div>
    );
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
        <button
          onClick={onBack}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          style={{ color: 'var(--emerald-green)' }}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />
          </svg>
        </button>
        <div className={`${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
          <h2 className={`text-3xl font-bold ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
            {currentSpec.title}
          </h2>
          <p className={`text-gray-600 mt-2 ${language === 'ar' ? 'font-arabic' : ''}`}>
            {framework.toUpperCase()} Framework
          </p>
        </div>
      </div>

      {/* Content */}
      <div className={`flex gap-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* Navigation */}
        <div className="w-64">
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h3 className={`text-lg font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--emerald-green)' }}>
              {language === 'en' ? 'Sections' : 'الأقسام'}
            </h3>
            <nav className="space-y-2">
              <button
                onClick={() => setActiveSection('overview')}
                className={`w-full text-left p-3 rounded-lg transition-colors ${
                  activeSection === 'overview' ? 'bg-emerald-50 text-emerald-700' : 'hover:bg-gray-50'
                } ${language === 'ar' ? 'text-right font-arabic' : ''}`}
              >
                {language === 'en' ? 'Overview' : 'نظرة عامة'}
              </button>
              {currentSpec.sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full text-left p-3 rounded-lg transition-colors ${
                    activeSection === section.id ? 'bg-emerald-50 text-emerald-700' : 'hover:bg-gray-50'
                  } ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                >
                  {section.title}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <div className="bg-white rounded-2xl shadow-lg p-8">
            {activeSection === 'overview' ? (
              <div>
                <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {language === 'en' ? 'Overview' : 'نظرة عامة'}
                </h3>
                <p className={`text-gray-700 leading-relaxed ${language === 'ar' ? 'font-arabic' : ''}`}>
                  {currentSpec.overview}
                </p>
              </div>
            ) : (
              <div>
                {currentSpec.sections.map((section) => (
                  activeSection === section.id && (
                    <div key={section.id}>
                      <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                        {section.title}
                      </h3>
                      <p className={`text-gray-700 leading-relaxed ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {section.content}
                      </p>
                    </div>
                  )
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
