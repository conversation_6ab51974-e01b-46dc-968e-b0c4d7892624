'use client';

import { useState, useEffect } from 'react';

interface EALayersProps {
  language: 'en' | 'ar';
  onLayerClick: (layer: string) => void;
}

export default function EALayers({ language, onLayerClick }: EALayersProps) {
  const [selectedLayer, setSelectedLayer] = useState<string | null>(null);
  const [animationPhase, setAnimationPhase] = useState(0);

  const layers = [
    {
      id: 'business',
      name: { en: 'Business Architecture', ar: 'هندسة الأعمال' },
      description: { en: 'Business processes, capabilities, and organization', ar: 'العمليات التجارية والقدرات والتنظيم' },
      color: '#026c4a',
      icon: '🏢'
    },
    {
      id: 'information',
      name: { en: 'Information Architecture', ar: 'هندسة المعلومات' },
      description: { en: 'Data models, information flows, and governance', ar: 'نماذج البيانات وتدفقات المعلومات والحوكمة' },
      color: '#0c402e',
      icon: '📊'
    },
    {
      id: 'application',
      name: { en: 'Application Architecture', ar: 'هندسة التطبيقات' },
      description: { en: 'Software applications and their interactions', ar: 'تطبيقات البرمجيات وتفاعلاتها' },
      color: '#026c4a',
      icon: '💻'
    },
    {
      id: 'technology',
      name: { en: 'Technology Architecture', ar: 'هندسة التكنولوجيا' },
      description: { en: 'Infrastructure, platforms, and technical standards', ar: 'البنية التحتية والمنصات والمعايير التقنية' },
      color: '#0c402e',
      icon: '⚙️'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 4);
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  const handleLayerClick = (layer: any) => {
    setSelectedLayer(layer.id);
    onLayerClick(layer.id);
  };

  return (
    <div className="flex flex-col items-center justify-center p-8">
      <h3 className={`text-2xl font-bold mb-8 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
        {language === 'en' ? 'Enterprise Architecture Layers' : 'طبقات هندسة المؤسسة'}
      </h3>

      <div className="relative w-full max-w-4xl">
        {/* 3D Isometric Layers */}
        <div className="relative h-96 perspective-1000">
          {layers.map((layer, index) => {
            const isActive = animationPhase === index;
            const isSelected = selectedLayer === layer.id;
            const zIndex = layers.length - index;
            const translateY = index * -20;
            const translateZ = index * 40;

            return (
              <div
                key={layer.id}
                className={`absolute inset-x-0 cursor-pointer transition-all duration-700 transform-gpu ${
                  isSelected ? 'scale-105' : 'hover:scale-102'
                }`}
                style={{
                  top: `${60 + index * 60}px`,
                  height: '80px',
                  zIndex: zIndex,
                  transform: `translateY(${translateY}px) translateZ(${translateZ}px) rotateX(15deg)`,
                  transformStyle: 'preserve-3d'
                }}
                onClick={() => handleLayerClick(layer)}
              >
                {/* Layer Base */}
                <div
                  className={`relative w-full h-full rounded-lg shadow-lg border-2 transition-all duration-500 ${
                    isActive ? 'shadow-2xl border-white' : 'border-gray-300'
                  } ${isSelected ? 'ring-4 ring-emerald-500' : ''}`}
                  style={{
                    backgroundColor: layer.color,
                    background: `linear-gradient(135deg, ${layer.color} 0%, ${layer.color}dd 100%)`
                  }}
                >
                  {/* Layer Content */}
                  <div className={`flex items-center h-full px-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                    <div className={`flex-1 ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                      <h4 className={`text-xl font-bold text-white mb-1 ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {layer.name[language]}
                      </h4>
                      <p className={`text-white/80 text-sm ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {layer.description[language]}
                      </p>
                    </div>

                    {/* Layer Indicator */}
                    <div className={`w-8 h-8 rounded-full bg-white/20 flex items-center justify-center font-bold text-white ${language === 'ar' ? 'mr-4' : 'ml-4'}`}>
                      {index + 1}
                    </div>
                  </div>

                  {/* Active Layer Highlight */}
                  {isActive && (
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-lg animate-pulse"></div>
                  )}
                </div>

                {/* Layer Side (3D Effect) */}
                <div
                  className="absolute top-0 left-0 w-full h-full rounded-lg opacity-60"
                  style={{
                    backgroundColor: layer.color,
                    transform: 'translateY(-4px) translateX(4px)',
                    zIndex: -1,
                    filter: 'brightness(0.8)'
                  }}
                ></div>
              </div>
            );
          })}
        </div>

        {/* Layer Connections */}
        <div className="absolute left-1/2 top-20 bottom-20 w-0.5 bg-gray-300 transform -translate-x-1/2 opacity-30"></div>

        {/* Side Labels */}
        <div className={`absolute top-0 ${language === 'ar' ? 'left-0' : 'right-0'} h-full flex flex-col justify-center space-y-12 ${language === 'ar' ? 'pr-8' : 'pl-8'}`}>
          {layers.map((layer, index) => (
            <div
              key={index}
              className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}
            >
              <div
                className={`w-4 h-4 rounded-full transition-all duration-500 ${
                  animationPhase === index ? 'scale-150 shadow-lg' : ''
                }`}
                style={{
                  backgroundColor: animationPhase === index ? layer.color : '#d1d5db'
                }}
              ></div>
              <span className={`text-sm font-medium ${language === 'ar' ? 'mr-3 font-arabic' : 'ml-3'}`} style={{ color: 'var(--charcoal-grey)' }}>
                {language === 'en' ? `Layer ${index + 1}` : `الطبقة ${index + 1}`}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
