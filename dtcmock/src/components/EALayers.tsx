'use client';

import { useState, useEffect } from 'react';

interface EALayersProps {
  language: 'en' | 'ar';
  onLayerClick: (layer: string) => void;
}

export default function EALayers({ language, onLayerClick }: EALayersProps) {
  const [selectedLayer, setSelectedLayer] = useState<string | null>(null);
  const [animationPhase, setAnimationPhase] = useState(0);

  const layers = [
    {
      id: 'business',
      name: { en: 'Business Architecture', ar: 'هندسة الأعمال' },
      description: { en: 'Business processes, capabilities, and organization', ar: 'العمليات التجارية والقدرات والتنظيم' },
      color: '#026c4a',
      icon: '🏢'
    },
    {
      id: 'information',
      name: { en: 'Information Architecture', ar: 'هندسة المعلومات' },
      description: { en: 'Data models, information flows, and governance', ar: 'نماذج البيانات وتدفقات المعلومات والحوكمة' },
      color: '#0c402e',
      icon: '📊'
    },
    {
      id: 'application',
      name: { en: 'Application Architecture', ar: 'هندسة التطبيقات' },
      description: { en: 'Software applications and their interactions', ar: 'تطبيقات البرمجيات وتفاعلاتها' },
      color: '#026c4a',
      icon: '💻'
    },
    {
      id: 'technology',
      name: { en: 'Technology Architecture', ar: 'هندسة التكنولوجيا' },
      description: { en: 'Infrastructure, platforms, and technical standards', ar: 'البنية التحتية والمنصات والمعايير التقنية' },
      color: '#0c402e',
      icon: '⚙️'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 4);
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  const handleLayerClick = (layer: any) => {
    setSelectedLayer(layer.id);
    onLayerClick(layer.id);
  };

  return (
    <div className="flex flex-col items-center justify-center p-8">
      <h3 className={`text-2xl font-bold mb-8 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
        {language === 'en' ? 'Enterprise Architecture Layers' : 'طبقات هندسة المؤسسة'}
      </h3>
      
      <div className="relative w-full max-w-2xl">
        {/* EA Layers Stack */}
        <div className="space-y-4">
          {layers.map((layer, index) => {
            const isActive = animationPhase === index;
            const isSelected = selectedLayer === layer.id;
            
            return (
              <div
                key={layer.id}
                className={`relative cursor-pointer transition-all duration-700 transform ${
                  isActive ? 'scale-105 shadow-2xl' : 'hover:scale-102 hover:shadow-lg'
                } ${isSelected ? 'ring-4 ring-white scale-105' : ''}`}
                onClick={() => handleLayerClick(layer)}
                style={{
                  animationDelay: `${index * 0.2}s`
                }}
              >
                <div 
                  className={`p-8 rounded-2xl text-white relative overflow-hidden ${
                    isActive ? 'animate-pulse-subtle' : ''
                  }`}
                  style={{ backgroundColor: layer.color }}
                >
                  {/* Animated Background Effect */}
                  {isActive && (
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                  )}
                  
                  {/* Layer Content */}
                  <div className={`relative flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
                    <div className="text-4xl mr-6">{layer.icon}</div>
                    <div className={`flex-1 ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                      <h4 className={`text-2xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {layer.name[language]}
                      </h4>
                      <p className={`text-white/90 ${language === 'ar' ? 'font-arabic' : ''}`}>
                        {layer.description[language]}
                      </p>
                    </div>
                    
                    {/* Layer Number */}
                    <div className={`w-12 h-12 rounded-full bg-white/20 flex items-center justify-center font-bold text-lg ${language === 'ar' ? 'mr-6' : 'ml-6'}`}>
                      {index + 1}
                    </div>
                  </div>

                  {/* Connection Lines */}
                  {index < layers.length - 1 && (
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full">
                      <div className="w-1 h-4 bg-gray-300 opacity-50"></div>
                      <div className="w-3 h-3 bg-gray-400 rounded-full transform -translate-x-1/2"></div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Side Indicators */}
        <div className={`absolute top-0 ${language === 'ar' ? 'left-0' : 'right-0'} transform ${language === 'ar' ? '-translate-x-16' : 'translate-x-16'} h-full flex flex-col justify-around`}>
          {layers.map((_, index) => (
            <div
              key={index}
              className={`w-3 h-3 rounded-full transition-all duration-500 ${
                animationPhase === index ? 'bg-emerald-500 scale-150' : 'bg-gray-300'
              }`}
            ></div>
          ))}
        </div>
      </div>

      {/* Selected Layer Details */}
      {selectedLayer && (
        <div className="mt-8 p-6 bg-white rounded-2xl shadow-lg border border-gray-200 max-w-2xl">
          <h4 className={`text-xl font-bold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--emerald-green)' }}>
            {layers.find(l => l.id === selectedLayer)?.name[language]}
          </h4>
          <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`}>
            {language === 'en' 
              ? 'Click to explore detailed specifications, standards, and implementation guidelines for this enterprise architecture layer.'
              : 'انقر لاستكشاف المواصفات التفصيلية والمعايير وإرشادات التنفيذ لطبقة هندسة المؤسسة هذه.'
            }
          </p>
        </div>
      )}
    </div>
  );
}
