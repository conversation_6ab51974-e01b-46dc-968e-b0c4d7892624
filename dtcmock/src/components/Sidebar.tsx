'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface SidebarProps {
  userRole: 'admin' | 'consultant' | 'project-manager' | 'trainee';
  language: 'en' | 'ar';
  onLanguageChange: (lang: 'en' | 'ar') => void;
}

export default function Sidebar({ userRole, language, onLanguageChange }: SidebarProps) {
  const router = useRouter();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const content = {
    en: {
      home: 'Home',
      userManagement: 'User Management',
      frameworkManagement: 'Framework Management',
      projects: 'Projects',
      trainingCourses: 'Training Courses',
      logout: 'Logout',
      profile: 'Profile',
      toggleSidebar: 'Toggle Sidebar'
    },
    ar: {
      home: 'الرئيسية',
      userManagement: 'إدارة المستخدمين',
      frameworkManagement: 'إدارة الإطار',
      projects: 'المشاريع',
      trainingCourses: 'الدورات التدريبية',
      logout: 'تسجيل الخروج',
      profile: 'الملف الشخصي',
      toggleSidebar: 'تبديل الشريط الجانبي'
    }
  };

  const adminMenuItems = [
    { key: 'home', label: content[language].home, href: '/dashboard', icon: '🏠' },
    { key: 'userManagement', label: content[language].userManagement, href: '/dashboard/users', icon: '👥' },
    { key: 'frameworkManagement', label: content[language].frameworkManagement, href: '/dashboard/frameworks', icon: '🏗️' },
    { key: 'projects', label: content[language].projects, href: '/dashboard/projects', icon: '📊' },
    { key: 'trainingCourses', label: content[language].trainingCourses, href: '/dashboard/training', icon: '🎓' }
  ];

  const otherRoleMenuItems = [
    { key: 'home', label: content[language].home, href: '/dashboard', icon: '🏠' }
  ];

  const menuItems = userRole === 'admin' ? adminMenuItems : otherRoleMenuItems;

  const handleLogout = () => {
    // Mock logout - redirect to login
    router.push('/login');
  };

  return (
    <div 
      className={`fixed top-0 h-full bg-white shadow-lg transition-all duration-300 z-50 ${
        isCollapsed ? 'w-16' : 'w-64'
      } ${language === 'ar' ? 'right-0' : 'left-0'}`}
      dir={language === 'ar' ? 'rtl' : 'ltr'}
      style={{ borderColor: 'var(--charcoal-grey)' }}
    >
      {/* Header with Profile */}
      <div className="p-4 border-b" style={{ borderColor: 'var(--charcoal-grey)' }}>
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              {/* Profile Picture */}
              <div 
                className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg"
                style={{ backgroundColor: 'var(--emerald-green)' }}
              >
                👤
              </div>
              <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
                <p className={`font-medium text-sm ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {content[language].profile}
                </p>
                <p className={`text-xs opacity-70 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                  {userRole.charAt(0).toUpperCase() + userRole.slice(1).replace('-', ' ')}
                </p>
              </div>
            </div>
          )}
          
          {/* Toggle Button */}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            title={content[language].toggleSidebar}
          >
            <span className="text-lg">
              {isCollapsed ? '→' : (language === 'ar' ? '←' : '→')}
            </span>
          </button>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => (
            <li key={item.key}>
              <Link
                href={item.href}
                className={`nav-item flex items-center p-3 rounded-lg group ${
                  language === 'ar' ? 'flex-row-reverse' : 'flex-row'
                }`}
                style={{ color: 'var(--charcoal-grey)' }}
              >
                <span className="text-lg">{item.icon}</span>
                {!isCollapsed && (
                  <span 
                    className={`font-medium ${language === 'ar' ? 'font-arabic mr-3' : 'ml-3'}`}
                  >
                    {item.label}
                  </span>
                )}
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      {/* Footer with Language Toggle and Logout */}
      <div className="p-4 border-t" style={{ borderColor: 'var(--charcoal-grey)' }}>
        {!isCollapsed && (
          <div className="space-y-3">
            {/* Language Toggle */}
            <button
              onClick={() => onLanguageChange(language === 'en' ? 'ar' : 'en')}
              className={`sidebar-button w-full flex items-center p-2 rounded-lg hover:bg-gray-100 ${
                language === 'ar' ? 'flex-row-reverse' : 'flex-row'
              }`}
              style={{ color: 'var(--charcoal-grey)' }}
            >
              <span className="text-lg">🌐</span>
              <span className={`font-medium ${language === 'ar' ? 'font-arabic mr-3' : 'ml-3'}`}>
                {language === 'en' ? 'العربية' : 'English'}
              </span>
            </button>

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              className={`sidebar-button w-full flex items-center p-2 rounded-lg hover:bg-red-50 text-red-600 ${
                language === 'ar' ? 'flex-row-reverse' : 'flex-row'
              }`}
            >
              <span className="text-lg">🚪</span>
              <span className={`font-medium ${language === 'ar' ? 'font-arabic mr-3' : 'ml-3'}`}>
                {content[language].logout}
              </span>
            </button>
          </div>
        )}
        
        {isCollapsed && (
          <div className="space-y-3 flex flex-col items-center">
            {/* Language Toggle - Collapsed */}
            <button
              onClick={() => onLanguageChange(language === 'en' ? 'ar' : 'en')}
              className="p-2 rounded-lg transition-colors hover:bg-gray-100"
              style={{ color: 'var(--charcoal-grey)' }}
              title={language === 'en' ? 'العربية' : 'English'}
            >
              <span className="text-lg">🌐</span>
            </button>

            {/* Logout Button - Collapsed */}
            <button
              onClick={handleLogout}
              className="p-2 rounded-lg transition-colors hover:bg-red-50 text-red-600"
              title={content[language].logout}
            >
              <span className="text-lg">🚪</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
