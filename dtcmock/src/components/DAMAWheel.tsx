'use client';

import { useState, useEffect } from 'react';

interface DAMAWheelProps {
  language: 'en' | 'ar';
  onDomainClick: (domain: string) => void;
}

export default function DAMAWheel({ language, onDomainClick }: DAMAWheelProps) {
  const [selectedDomain, setSelectedDomain] = useState<string | null>(null);

  const domains = [
    {
      id: 'data-governance',
      name: { en: 'Data Governance', ar: 'حوكمة البيانات' },
      angle: 0,
      color: '#026c4a'
    },
    {
      id: 'data-architecture',
      name: { en: 'Data Architecture', ar: 'هندسة البيانات' },
      angle: 45,
      color: '#0c402e'
    },
    {
      id: 'data-modeling',
      name: { en: 'Data Modeling', ar: 'نمذجة البيانات' },
      angle: 90,
      color: '#026c4a'
    },
    {
      id: 'data-storage',
      name: { en: 'Data Storage', ar: 'تخزين البيانات' },
      angle: 135,
      color: '#0c402e'
    },
    {
      id: 'data-security',
      name: { en: 'Data Security', ar: 'أمان البيانات' },
      angle: 180,
      color: '#026c4a'
    },
    {
      id: 'data-integration',
      name: { en: 'Data Integration', ar: 'تكامل البيانات' },
      angle: 225,
      color: '#0c402e'
    },
    {
      id: 'data-quality',
      name: { en: 'Data Quality', ar: 'جودة البيانات' },
      angle: 270,
      color: '#026c4a'
    },
    {
      id: 'metadata',
      name: { en: 'Metadata', ar: 'البيانات الوصفية' },
      angle: 315,
      color: '#0c402e'
    }
  ];

  const handleDomainClick = (domain: any) => {
    setSelectedDomain(domain.id);
    onDomainClick(domain.id);
  };

  return (
    <div className="flex flex-col items-center justify-center p-8">
      <h3 className={`text-2xl font-bold mb-8 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
        {language === 'en' ? 'DAMA Data Management Domains' : 'مجالات إدارة البيانات DAMA'}
      </h3>
      
      <div className="relative w-96 h-96">
        {/* Center Circle */}
        <div 
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-2xl animate-pulse-subtle"
          style={{ backgroundColor: 'var(--emerald-green)' }}
        >
          <span className={language === 'ar' ? 'font-arabic' : ''}>
            {language === 'en' ? 'DAMA' : 'داما'}
          </span>
        </div>

        {/* Domain Segments */}
        {domains.map((domain, index) => {
          const radius = 150;
          const centerX = 192;
          const centerY = 192;
          const angleRad = (domain.angle * Math.PI) / 180;
          const x = centerX + radius * Math.cos(angleRad);
          const y = centerY + radius * Math.sin(angleRad);

          return (
            <div
              key={domain.id}
              className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-500 hover:scale-110 ${
                selectedDomain === domain.id ? 'scale-125 z-10' : ''
              }`}
              style={{
                left: x,
                top: y,
                animationDelay: `${index * 0.1}s`
              }}
              onClick={() => handleDomainClick(domain)}
            >
              <div 
                className={`w-20 h-20 rounded-full flex items-center justify-center text-white font-semibold text-xs shadow-lg hover:shadow-2xl transition-all duration-300 ${
                  selectedDomain === domain.id ? 'ring-4 ring-white' : ''
                }`}
                style={{ 
                  backgroundColor: domain.color,
                  animation: 'fadeInUp 0.6s ease-out forwards'
                }}
              >
                <span className={`text-center leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>
                  {domain.name[language]}
                </span>
              </div>
              
              {/* Connection Line */}
              <div 
                className="absolute top-1/2 left-1/2 origin-left h-0.5 bg-gray-300 opacity-50"
                style={{
                  width: `${radius - 60}px`,
                  transform: `translate(-50%, -50%) rotate(${domain.angle + 180}deg)`,
                  transformOrigin: 'left center'
                }}
              ></div>
            </div>
          );
        })}

        {/* Rotating Ring Animation */}
        <div className="absolute inset-0 border-4 border-dashed border-gray-300 rounded-full opacity-30 animate-spin" style={{ animationDuration: '20s' }}></div>
      </div>

      {/* Selected Domain Info */}
      {selectedDomain && (
        <div className="mt-8 p-6 bg-white rounded-2xl shadow-lg border border-gray-200 max-w-md">
          <h4 className={`text-xl font-bold mb-2 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`} style={{ color: 'var(--emerald-green)' }}>
            {domains.find(d => d.id === selectedDomain)?.name[language]}
          </h4>
          <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic text-right' : 'text-left'}`}>
            {language === 'en' 
              ? 'Click to explore detailed specifications and implementation guidelines for this data management domain.'
              : 'انقر لاستكشاف المواصفات التفصيلية وإرشادات التنفيذ لمجال إدارة البيانات هذا.'
            }
          </p>
        </div>
      )}
    </div>
  );
}
